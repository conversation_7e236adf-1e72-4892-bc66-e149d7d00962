// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class S {
  S();

  static S? _current;

  static S get current {
    assert(_current != null,
        'No instance of S was loaded. Try to initialize the S delegate before accessing S.current.');
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<S> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = S();
      S._current = instance;

      return instance;
    });
  }

  static S of(BuildContext context) {
    final instance = S.maybeOf(context);
    assert(instance != null,
        'No instance of S present in the widget tree. Did you add S.delegate in localizationsDelegates?');
    return instance!;
  }

  static S? maybeOf(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  /// `Hello`
  String get hello {
    return Intl.message(
      'Hello',
      name: 'hello',
      desc: '',
      args: [],
    );
  }

  /// `Cancel`
  String get text_cancel {
    return Intl.message(
      'Cancel',
      name: 'text_cancel',
      desc: '',
      args: [],
    );
  }

  /// `Confirm`
  String get text_confirm {
    return Intl.message(
      'Confirm',
      name: 'text_confirm',
      desc: '',
      args: [],
    );
  }

  /// `Close`
  String get text_close {
    return Intl.message(
      'Close',
      name: 'text_close',
      desc: '',
      args: [],
    );
  }

  /// `Something went wrong`
  String get text_error_something_wrong {
    return Intl.message(
      'Something went wrong',
      name: 'text_error_something_wrong',
      desc: '',
      args: [],
    );
  }

  /// `Welcome To TripC`
  String get text_welcome_to_tripC {
    return Intl.message(
      'Welcome To TripC',
      name: 'text_welcome_to_tripC',
      desc: '',
      args: [],
    );
  }

  /// `Enter your account information!`
  String get text_pls_enter_details {
    return Intl.message(
      'Enter your account information!',
      name: 'text_pls_enter_details',
      desc: '',
      args: [],
    );
  }

  /// `Email`
  String get text_email {
    return Intl.message(
      'Email',
      name: 'text_email',
      desc: '',
      args: [],
    );
  }

  /// `Enter your email`
  String get text_enter_email {
    return Intl.message(
      'Enter your email',
      name: 'text_enter_email',
      desc: '',
      args: [],
    );
  }

  /// `Or`
  String get text_or {
    return Intl.message(
      'Or',
      name: 'text_or',
      desc: '',
      args: [],
    );
  }

  /// `Continue with Google`
  String get text_continue_with_google {
    return Intl.message(
      'Continue with Google',
      name: 'text_continue_with_google',
      desc: '',
      args: [],
    );
  }

  /// `Continue with Facebook`
  String get text_continue_with_facebook {
    return Intl.message(
      'Continue with Facebook',
      name: 'text_continue_with_facebook',
      desc: '',
      args: [],
    );
  }

  /// `Continue with Apple`
  String get text_continue_with_apple {
    return Intl.message(
      'Continue with Apple',
      name: 'text_continue_with_apple',
      desc: '',
      args: [],
    );
  }

  /// `Sign Up With Google`
  String get text_sign_up_with_google {
    return Intl.message(
      'Sign Up With Google',
      name: 'text_sign_up_with_google',
      desc: '',
      args: [],
    );
  }

  /// `Sign Up With Facebook`
  String get text_sign_up_with_facebook {
    return Intl.message(
      'Sign Up With Facebook',
      name: 'text_sign_up_with_facebook',
      desc: '',
      args: [],
    );
  }

  /// `Sign Up With Apple`
  String get text_sign_up_with_apple {
    return Intl.message(
      'Sign Up With Apple',
      name: 'text_sign_up_with_apple',
      desc: '',
      args: [],
    );
  }

  /// `Remember me`
  String get text_remember_me {
    return Intl.message(
      'Remember me',
      name: 'text_remember_me',
      desc: '',
      args: [],
    );
  }

  /// `Forgot password`
  String get text_forgot_password_lower {
    return Intl.message(
      'Forgot password',
      name: 'text_forgot_password_lower',
      desc: '',
      args: [],
    );
  }

  /// `You don't have an account?`
  String get text_dont_have_account {
    return Intl.message(
      'You don\'t have an account?',
      name: 'text_dont_have_account',
      desc: '',
      args: [],
    );
  }

  /// `Sign up`
  String get text_sign_up {
    return Intl.message(
      'Sign up',
      name: 'text_sign_up',
      desc: '',
      args: [],
    );
  }

  /// `Create Account`
  String get text_create_account {
    return Intl.message(
      'Create Account',
      name: 'text_create_account',
      desc: '',
      args: [],
    );
  }

  /// `Full name`
  String get text_full_name {
    return Intl.message(
      'Full name',
      name: 'text_full_name',
      desc: '',
      args: [],
    );
  }

  /// `Full name`
  String get text_first_name_and_last_name {
    return Intl.message(
      'Full name',
      name: 'text_first_name_and_last_name',
      desc: '',
      args: [],
    );
  }

  /// `Enter your name`
  String get text_enter_your_name {
    return Intl.message(
      'Enter your name',
      name: 'text_enter_your_name',
      desc: '',
      args: [],
    );
  }

  /// `Sign in`
  String get text_sign_in {
    return Intl.message(
      'Sign in',
      name: 'text_sign_in',
      desc: '',
      args: [],
    );
  }

  /// `Do you have an account?`
  String get text_do_have_account {
    return Intl.message(
      'Do you have an account?',
      name: 'text_do_have_account',
      desc: '',
      args: [],
    );
  }

  /// `Password`
  String get text_password {
    return Intl.message(
      'Password',
      name: 'text_password',
      desc: '',
      args: [],
    );
  }

  /// `Enter your password`
  String get text_enter_your_password {
    return Intl.message(
      'Enter your password',
      name: 'text_enter_your_password',
      desc: '',
      args: [],
    );
  }

  /// `Forgot Password`
  String get text_forgot_password {
    return Intl.message(
      'Forgot Password',
      name: 'text_forgot_password',
      desc: '',
      args: [],
    );
  }

  /// `Enter your Email account to reset your password`
  String get text_enter_mail_reset_pass {
    return Intl.message(
      'Enter your Email account to reset your password',
      name: 'text_enter_mail_reset_pass',
      desc: '',
      args: [],
    );
  }

  /// `Send OTP`
  String get text_send_otp {
    return Intl.message(
      'Send OTP',
      name: 'text_send_otp',
      desc: '',
      args: [],
    );
  }

  /// `Send OTP Code`
  String get text_send_otp_code {
    return Intl.message(
      'Send OTP Code',
      name: 'text_send_otp_code',
      desc: '',
      args: [],
    );
  }

  /// `Enter`
  String get text_enter {
    return Intl.message(
      'Enter',
      name: 'text_enter',
      desc: '',
      args: [],
    );
  }

  /// `Get started`
  String get text_get_started {
    return Intl.message(
      'Get started',
      name: 'text_get_started',
      desc: '',
      args: [],
    );
  }

  /// `Skip`
  String get text_skip {
    return Intl.message(
      'Skip',
      name: 'text_skip',
      desc: '',
      args: [],
    );
  }

  /// `Explore The World`
  String get text_explore_the_word {
    return Intl.message(
      'Explore The World',
      name: 'text_explore_the_word',
      desc: '',
      args: [],
    );
  }

  /// `Discover new places, cultures, and experiences , Unlock the doors to adventure and wanderlust`
  String get text_explore_the_word_content {
    return Intl.message(
      'Discover new places, cultures, and experiences , Unlock the doors to adventure and wanderlust',
      name: 'text_explore_the_word_content',
      desc: '',
      args: [],
    );
  }

  /// `Choose Destination`
  String get text_choose_destination {
    return Intl.message(
      'Choose Destination',
      name: 'text_choose_destination',
      desc: '',
      args: [],
    );
  }

  /// `Find your perfect vacation spot, Find your perfect getaway and plan your trip`
  String get text_choose_destination_content {
    return Intl.message(
      'Find your perfect vacation spot, Find your perfect getaway and plan your trip',
      name: 'text_choose_destination_content',
      desc: '',
      args: [],
    );
  }

  /// `Enjoy Your Trip`
  String get text_enjoy_your_trip {
    return Intl.message(
      'Enjoy Your Trip',
      name: 'text_enjoy_your_trip',
      desc: '',
      args: [],
    );
  }

  /// `Use referral code to receive the offer`
  String get text_referral_code {
    return Intl.message(
      'Use referral code to receive the offer',
      name: 'text_referral_code',
      desc: '',
      args: [],
    );
  }

  /// `Enter the referral code from your friend to immediately receive a special gift from us.`
  String get text_enter_referral_code {
    return Intl.message(
      'Enter the referral code from your friend to immediately receive a special gift from us.',
      name: 'text_enter_referral_code',
      desc: '',
      args: [],
    );
  }

  /// `Enter your referral code`
  String get text_enter_your_referral_code {
    return Intl.message(
      'Enter your referral code',
      name: 'text_enter_your_referral_code',
      desc: '',
      args: [],
    );
  }

  /// `The referral code is the TripC ID provided by a friend or relative when they have used the application.\nYou can skip this step and enter the code later if you do not have a referral code.`
  String get text_referral_code_define {
    return Intl.message(
      'The referral code is the TripC ID provided by a friend or relative when they have used the application.\nYou can skip this step and enter the code later if you do not have a referral code.',
      name: 'text_referral_code_define',
      desc: '',
      args: [],
    );
  }

  /// `Forgot passcode`
  String get text_forgot_passcode {
    return Intl.message(
      'Forgot passcode',
      name: 'text_forgot_passcode',
      desc: '',
      args: [],
    );
  }

  /// `Enter your Email account to reset your Passcode`
  String get text_enter_mail_reset_passcode {
    return Intl.message(
      'Enter your Email account to reset your Passcode',
      name: 'text_enter_mail_reset_passcode',
      desc: '',
      args: [],
    );
  }

  /// `Successful payment!`
  String get text_success_payment {
    return Intl.message(
      'Successful payment!',
      name: 'text_success_payment',
      desc: '',
      args: [],
    );
  }

  /// `Order successfully!`
  String get text_success_order {
    return Intl.message(
      'Order successfully!',
      name: 'text_success_order',
      desc: '',
      args: [],
    );
  }

  /// `Order failed!`
  String get text_failure_order {
    return Intl.message(
      'Order failed!',
      name: 'text_failure_order',
      desc: '',
      args: [],
    );
  }

  /// `Congratulations on owning a meaningful series of numbers.\nNext, create a Passcode for your account!`
  String get text_congratulation_and_make_passcode {
    return Intl.message(
      'Congratulations on owning a meaningful series of numbers.\nNext, create a Passcode for your account!',
      name: 'text_congratulation_and_make_passcode',
      desc: '',
      args: [],
    );
  }

  /// `Create passcode`
  String get text_create_passcode {
    return Intl.message(
      'Create passcode',
      name: 'text_create_passcode',
      desc: '',
      args: [],
    );
  }

  /// `You are choosing`
  String get text_you_are_choosing {
    return Intl.message(
      'You are choosing',
      name: 'text_you_are_choosing',
      desc: '',
      args: [],
    );
  }

  /// `Pay`
  String get text_pay {
    return Intl.message(
      'Pay',
      name: 'text_pay',
      desc: '',
      args: [],
    );
  }

  /// `Scan qr code`
  String get text_scan_qr {
    return Intl.message(
      'Scan qr code',
      name: 'text_scan_qr',
      desc: '',
      args: [],
    );
  }

  /// `CT: Pay bills TripC ID at TripC`
  String get text_payment_content {
    return Intl.message(
      'CT: Pay bills TripC ID at TripC',
      name: 'text_payment_content',
      desc: '',
      args: [],
    );
  }

  /// `Choose TripC ID beautiful number`
  String get text_choose_beautiful_number {
    return Intl.message(
      'Choose TripC ID beautiful number',
      name: 'text_choose_beautiful_number',
      desc: '',
      args: [],
    );
  }

  /// `Please enter TripC ID (If any)`
  String get text_lets_enter_your_tripc_id {
    return Intl.message(
      'Please enter TripC ID (If any)',
      name: 'text_lets_enter_your_tripc_id',
      desc: '',
      args: [],
    );
  }

  /// `Enter your TripC ID`
  String get text_enter_your_tripc_id {
    return Intl.message(
      'Enter your TripC ID',
      name: 'text_enter_your_tripc_id',
      desc: '',
      args: [],
    );
  }

  /// `Enter TripC ID Passcode`
  String get text_enter_your_tripc_passcode {
    return Intl.message(
      'Enter TripC ID Passcode',
      name: 'text_enter_your_tripc_passcode',
      desc: '',
      args: [],
    );
  }

  /// `Forgot passcode?`
  String get text_suggest_for_forgot_passcode {
    return Intl.message(
      'Forgot passcode?',
      name: 'text_suggest_for_forgot_passcode',
      desc: '',
      args: [],
    );
  }

  /// `Register new TripC ID`
  String get text_register_new_id {
    return Intl.message(
      'Register new TripC ID',
      name: 'text_register_new_id',
      desc: '',
      args: [],
    );
  }

  /// `Please choose your own TripC ID`
  String get text_pls_choose_your_own_tripc_id {
    return Intl.message(
      'Please choose your own TripC ID',
      name: 'text_pls_choose_your_own_tripc_id',
      desc: '',
      args: [],
    );
  }

  /// `TripC ID free`
  String get text_tripc_id_free {
    return Intl.message(
      'TripC ID free',
      name: 'text_tripc_id_free',
      desc: '',
      args: [],
    );
  }

  /// `Completely free with full utilities and features in the TripC system`
  String get text_tripc_id_free_content {
    return Intl.message(
      'Completely free with full utilities and features in the TripC system',
      name: 'text_tripc_id_free_content',
      desc: '',
      args: [],
    );
  }

  /// `TripC ID nice number`
  String get text_tripc_id_nice_number {
    return Intl.message(
      'TripC ID nice number',
      name: 'text_tripc_id_nice_number',
      desc: '',
      args: [],
    );
  }

  /// `Choose a beautiful number now for instant fortune and good luck. Own it now!`
  String get text_tripc_id_nice_number_content {
    return Intl.message(
      'Choose a beautiful number now for instant fortune and good luck. Own it now!',
      name: 'text_tripc_id_nice_number_content',
      desc: '',
      args: [],
    );
  }

  /// `Enter TripC ID nice number`
  String get text_enter_tripc_id_nice_number {
    return Intl.message(
      'Enter TripC ID nice number',
      name: 'text_enter_tripc_id_nice_number',
      desc: '',
      args: [],
    );
  }

  /// `Enter min 2 numbers and max 6 numbers`
  String get text_enter_min_2_max_6 {
    return Intl.message(
      'Enter min 2 numbers and max 6 numbers',
      name: 'text_enter_min_2_max_6',
      desc: '',
      args: [],
    );
  }

  /// `Give you a TripC ID`
  String get text_give_a_tripc_id {
    return Intl.message(
      'Give you a TripC ID',
      name: 'text_give_a_tripc_id',
      desc: '',
      args: [],
    );
  }

  /// `Completely free with full utilities and features in the TripC system.\nNext, create a Passcode for your account!`
  String get text_lets_create_passcode {
    return Intl.message(
      'Completely free with full utilities and features in the TripC system.\nNext, create a Passcode for your account!',
      name: 'text_lets_create_passcode',
      desc: '',
      args: [],
    );
  }

  /// `Please enter 6-digit Passcode:`
  String get text_pls_enter_6_digit {
    return Intl.message(
      'Please enter 6-digit Passcode:',
      name: 'text_pls_enter_6_digit',
      desc: '',
      args: [],
    );
  }

  /// `Complete initialization of your membership card`
  String get text_finish_membership_card {
    return Intl.message(
      'Complete initialization of your membership card',
      name: 'text_finish_membership_card',
      desc: '',
      args: [],
    );
  }

  /// `Example:`
  String get text_example {
    return Intl.message(
      'Example:',
      name: 'text_example',
      desc: '',
      args: [],
    );
  }

  /// `Register now`
  String get text_register_now {
    return Intl.message(
      'Register now',
      name: 'text_register_now',
      desc: '',
      args: [],
    );
  }

  /// `Fees (excluding VAT)`
  String get text_fee {
    return Intl.message(
      'Fees (excluding VAT)',
      name: 'text_fee',
      desc: '',
      args: [],
    );
  }

  /// `VAT (10%)`
  String get text_vat {
    return Intl.message(
      'VAT (10%)',
      name: 'text_vat',
      desc: '',
      args: [],
    );
  }

  /// `Total payment`
  String get text_total_payment {
    return Intl.message(
      'Total payment',
      name: 'text_total_payment',
      desc: '',
      args: [],
    );
  }

  /// `In words`
  String get text_in_words {
    return Intl.message(
      'In words',
      name: 'text_in_words',
      desc: '',
      args: [],
    );
  }

  /// `Receive now`
  String get text_receive_now {
    return Intl.message(
      'Receive now',
      name: 'text_receive_now',
      desc: '',
      args: [],
    );
  }

  /// `Prosper number`
  String get text_prosper_number {
    return Intl.message(
      'Prosper number',
      name: 'text_prosper_number',
      desc: '',
      args: [],
    );
  }

  /// `Ngu quy number`
  String get text_nguquy_number {
    return Intl.message(
      'Ngu quy number',
      name: 'text_nguquy_number',
      desc: '',
      args: [],
    );
  }

  /// `Fengshui number`
  String get text_fengshui_number {
    return Intl.message(
      'Fengshui number',
      name: 'text_fengshui_number',
      desc: '',
      args: [],
    );
  }

  /// `Search`
  String get text_search {
    return Intl.message(
      'Search',
      name: 'text_search',
      desc: '',
      args: [],
    );
  }

  /// `GET NOW`
  String get text_get_now {
    return Intl.message(
      'GET NOW',
      name: 'text_get_now',
      desc: '',
      args: [],
    );
  }

  /// `EXTRA`
  String get text_extra {
    return Intl.message(
      'EXTRA',
      name: 'text_extra',
      desc: '',
      args: [],
    );
  }

  /// `DISCOUNT`
  String get text_discount_upper {
    return Intl.message(
      'DISCOUNT',
      name: 'text_discount_upper',
      desc: '',
      args: [],
    );
  }

  /// `Rating`
  String get text_rating {
    return Intl.message(
      'Rating',
      name: 'text_rating',
      desc: '',
      args: [],
    );
  }

  /// `From`
  String get text_from {
    return Intl.message(
      'From',
      name: 'text_from',
      desc: '',
      args: [],
    );
  }

  /// `Promotion`
  String get text_promotion {
    return Intl.message(
      'Promotion',
      name: 'text_promotion',
      desc: '',
      args: [],
    );
  }

  /// `Promotion around here`
  String get text_promotion_around_here {
    return Intl.message(
      'Promotion around here',
      name: 'text_promotion_around_here',
      desc: '',
      args: [],
    );
  }

  /// `Hot tour during Tet holiday`
  String get text_hot_tour_during_tet_holiday {
    return Intl.message(
      'Hot tour during Tet holiday',
      name: 'text_hot_tour_during_tet_holiday',
      desc: '',
      args: [],
    );
  }

  /// `Ha Long 2 days 1 night`
  String get text_danang_2d_1n {
    return Intl.message(
      'Ha Long 2 days 1 night',
      name: 'text_danang_2d_1n',
      desc: '',
      args: [],
    );
  }

  /// `I want to go to Ha Long`
  String get text_wanna_go_danang {
    return Intl.message(
      'I want to go to Ha Long',
      name: 'text_wanna_go_danang',
      desc: '',
      args: [],
    );
  }

  /// `Diamond`
  String get text_diamond {
    return Intl.message(
      'Diamond',
      name: 'text_diamond',
      desc: '',
      args: [],
    );
  }

  /// `Tcent`
  String get text_tcent {
    return Intl.message(
      'Tcent',
      name: 'text_tcent',
      desc: '',
      args: [],
    );
  }

  /// `Choose A Tour Date`
  String get text_choose_a_tour_date {
    return Intl.message(
      'Choose A Tour Date',
      name: 'text_choose_a_tour_date',
      desc: '',
      args: [],
    );
  }

  /// `Add quantity of passengers`
  String get text_add_quantity_passengers {
    return Intl.message(
      'Add quantity of passengers',
      name: 'text_add_quantity_passengers',
      desc: '',
      args: [],
    );
  }

  /// `Tour time: `
  String get text_tour_time {
    return Intl.message(
      'Tour time: ',
      name: 'text_tour_time',
      desc: '',
      args: [],
    );
  }

  /// `Departure location: `
  String get text_departure_location {
    return Intl.message(
      'Departure location: ',
      name: 'text_departure_location',
      desc: '',
      args: [],
    );
  }

  /// `Hotel: `
  String get text_hotel_space {
    return Intl.message(
      'Hotel: ',
      name: 'text_hotel_space',
      desc: '',
      args: [],
    );
  }

  /// `Restaurant: `
  String get text_restaurant_space {
    return Intl.message(
      'Restaurant: ',
      name: 'text_restaurant_space',
      desc: '',
      args: [],
    );
  }

  /// `Tour guide: `
  String get text_tour_guide {
    return Intl.message(
      'Tour guide: ',
      name: 'text_tour_guide',
      desc: '',
      args: [],
    );
  }

  /// `Adults (Over 10 years old)`
  String get text_adult_above_10 {
    return Intl.message(
      'Adults (Over 10 years old)',
      name: 'text_adult_above_10',
      desc: '',
      args: [],
    );
  }

  /// `Children (5 - 9 years old)`
  String get text_children_5_to_9 {
    return Intl.message(
      'Children (5 - 9 years old)',
      name: 'text_children_5_to_9',
      desc: '',
      args: [],
    );
  }

  /// `Information to note`
  String get text_information_to_note {
    return Intl.message(
      'Information to note',
      name: 'text_information_to_note',
      desc: '',
      args: [],
    );
  }

  /// `Total price`
  String get text_total_price {
    return Intl.message(
      'Total price',
      name: 'text_total_price',
      desc: '',
      args: [],
    );
  }

  /// `Surcharge included`
  String get text_surcharge_included {
    return Intl.message(
      'Surcharge included',
      name: 'text_surcharge_included',
      desc: '',
      args: [],
    );
  }

  /// `Book now`
  String get text_book_now {
    return Intl.message(
      'Book now',
      name: 'text_book_now',
      desc: '',
      args: [],
    );
  }

  /// `Add Passenger`
  String get text_add_passenger {
    return Intl.message(
      'Add Passenger',
      name: 'text_add_passenger',
      desc: '',
      args: [],
    );
  }

  /// `Passenger name must be entered exactly as it appears on your identification document`
  String get text_passenger_name_rule {
    return Intl.message(
      'Passenger name must be entered exactly as it appears on your identification document',
      name: 'text_passenger_name_rule',
      desc: '',
      args: [],
    );
  }

  /// `Principles on Passenger Names`
  String get text_principles_on_passenger_names {
    return Intl.message(
      'Principles on Passenger Names',
      name: 'text_principles_on_passenger_names',
      desc: '',
      args: [],
    );
  }

  /// `First name`
  String get text_first_name {
    return Intl.message(
      'First name',
      name: 'text_first_name',
      desc: '',
      args: [],
    );
  }

  /// `Last name`
  String get text_last_name {
    return Intl.message(
      'Last name',
      name: 'text_last_name',
      desc: '',
      args: [],
    );
  }

  /// `Edit information`
  String get text_edit_passenger {
    return Intl.message(
      'Edit information',
      name: 'text_edit_passenger',
      desc: '',
      args: [],
    );
  }

  /// `Fill in passenger information`
  String get text_fill_in_passenger_info {
    return Intl.message(
      'Fill in passenger information',
      name: 'text_fill_in_passenger_info',
      desc: '',
      args: [],
    );
  }

  /// `Go to Payment Page`
  String get text_go_to_payment_page {
    return Intl.message(
      'Go to Payment Page',
      name: 'text_go_to_payment_page',
      desc: '',
      args: [],
    );
  }

  /// `Departure date: `
  String get text_departure_date {
    return Intl.message(
      'Departure date: ',
      name: 'text_departure_date',
      desc: '',
      args: [],
    );
  }

  /// `Contact Information`
  String get text_contact_information {
    return Intl.message(
      'Contact Information',
      name: 'text_contact_information',
      desc: '',
      args: [],
    );
  }

  /// `Contact mobile phone number`
  String get text_mobile_phone {
    return Intl.message(
      'Contact mobile phone number',
      name: 'text_mobile_phone',
      desc: '',
      args: [],
    );
  }

  /// `Passenger information`
  String get text_passenger_information {
    return Intl.message(
      'Passenger information',
      name: 'text_passenger_information',
      desc: '',
      args: [],
    );
  }

  /// `Passenger Information`
  String get text_passenger_information_2 {
    return Intl.message(
      'Passenger Information',
      name: 'text_passenger_information_2',
      desc: '',
      args: [],
    );
  }

  /// `Add passenger information`
  String get text_add_passenger_information {
    return Intl.message(
      'Add passenger information',
      name: 'text_add_passenger_information',
      desc: '',
      args: [],
    );
  }

  /// `Special Requests (If any)`
  String get text_special_requests {
    return Intl.message(
      'Special Requests (If any)',
      name: 'text_special_requests',
      desc: '',
      args: [],
    );
  }

  /// `Add special request`
  String get text_add_special_request {
    return Intl.message(
      'Add special request',
      name: 'text_add_special_request',
      desc: '',
      args: [],
    );
  }

  /// `Powered by `
  String get text_powered_by {
    return Intl.message(
      'Powered by ',
      name: 'text_powered_by',
      desc: '',
      args: [],
    );
  }

  /// `General information`
  String get text_general_information {
    return Intl.message(
      'General information',
      name: 'text_general_information',
      desc: '',
      args: [],
    );
  }

  /// `Read more`
  String get text_read_more {
    return Intl.message(
      'Read more',
      name: 'text_read_more',
      desc: '',
      args: [],
    );
  }

  /// `Detailed information`
  String get text_detailed_information {
    return Intl.message(
      'Detailed information',
      name: 'text_detailed_information',
      desc: '',
      args: [],
    );
  }

  /// `Select time`
  String get text_select_service_package {
    return Intl.message(
      'Select time',
      name: 'text_select_service_package',
      desc: '',
      args: [],
    );
  }

  /// `Local time | The price displayed will be the lowest price for the current day`
  String get text_note_price_at_local_time {
    return Intl.message(
      'Local time | The price displayed will be the lowest price for the current day',
      name: 'text_note_price_at_local_time',
      desc: '',
      args: [],
    );
  }

  /// `Time frame`
  String get text_time_frame {
    return Intl.message(
      'Time frame',
      name: 'text_time_frame',
      desc: '',
      args: [],
    );
  }

  /// `Seat type`
  String get text_seat_type {
    return Intl.message(
      'Seat type',
      name: 'text_seat_type',
      desc: '',
      args: [],
    );
  }

  /// `Reviews outside the attractions`
  String get text_order_information_first_row {
    return Intl.message(
      'Reviews outside the attractions',
      name: 'text_order_information_first_row',
      desc: '',
      args: [],
    );
  }

  /// `Cancel when possible`
  String get text_order_information_second_row {
    return Intl.message(
      'Cancel when possible',
      name: 'text_order_information_second_row',
      desc: '',
      args: [],
    );
  }

  /// `Information about products/services.`
  String get text_order_information_third_row {
    return Intl.message(
      'Information about products/services.',
      name: 'text_order_information_third_row',
      desc: '',
      args: [],
    );
  }

  /// `Select`
  String get text_select {
    return Intl.message(
      'Select',
      name: 'text_select',
      desc: '',
      args: [],
    );
  }

  /// `Detail`
  String get text_detail {
    return Intl.message(
      'Detail',
      name: 'text_detail',
      desc: '',
      args: [],
    );
  }

  /// `respectively`
  String get text_respectively {
    return Intl.message(
      'respectively',
      name: 'text_respectively',
      desc: '',
      args: [],
    );
  }

  /// `See all`
  String get text_see_all {
    return Intl.message(
      'See all',
      name: 'text_see_all',
      desc: '',
      args: [],
    );
  }

  /// `Adult`
  String get text_aldult {
    return Intl.message(
      'Adult',
      name: 'text_aldult',
      desc: '',
      args: [],
    );
  }

  /// `children`
  String get text_children {
    return Intl.message(
      'children',
      name: 'text_children',
      desc: '',
      args: [],
    );
  }

  /// `Promotion Combo`
  String get text_promotion_combo {
    return Intl.message(
      'Promotion Combo',
      name: 'text_promotion_combo',
      desc: '',
      args: [],
    );
  }

  /// `Hotel`
  String get text_hotel {
    return Intl.message(
      'Hotel',
      name: 'text_hotel',
      desc: '',
      args: [],
    );
  }

  /// `Stay`
  String get text_stay {
    return Intl.message(
      'Stay',
      name: 'text_stay',
      desc: '',
      args: [],
    );
  }

  /// `Moving`
  String get text_moving {
    return Intl.message(
      'Moving',
      name: 'text_moving',
      desc: '',
      args: [],
    );
  }

  /// `Other Services`
  String get other_menu_services {
    return Intl.message(
      'Other Services',
      name: 'other_menu_services',
      desc: '',
      args: [],
    );
  }

  /// `Entertainment`
  String get entertainment {
    return Intl.message(
      'Entertainment',
      name: 'entertainment',
      desc: '',
      args: [],
    );
  }

  /// `Restaurant`
  String get text_restaurant {
    return Intl.message(
      'Restaurant',
      name: 'text_restaurant',
      desc: '',
      args: [],
    );
  }

  /// `Tour & SightSeeing`
  String get text_tour_sightseeing {
    return Intl.message(
      'Tour & SightSeeing',
      name: 'text_tour_sightseeing',
      desc: '',
      args: [],
    );
  }

  /// `Pick Up\nAirport`
  String get text_pick_up_airport {
    return Intl.message(
      'Pick Up\nAirport',
      name: 'text_pick_up_airport',
      desc: '',
      args: [],
    );
  }

  /// `Hire Vehicle`
  String get text_hire_vehicle {
    return Intl.message(
      'Hire Vehicle',
      name: 'text_hire_vehicle',
      desc: '',
      args: [],
    );
  }

  /// `Plane Ticket`
  String get text_ticket_plane {
    return Intl.message(
      'Plane Ticket',
      name: 'text_ticket_plane',
      desc: '',
      args: [],
    );
  }

  /// `Taxi`
  String get text_taxi {
    return Intl.message(
      'Taxi',
      name: 'text_taxi',
      desc: '',
      args: [],
    );
  }

  /// `Promotion`
  String get text_promotion_2nd {
    return Intl.message(
      'Promotion',
      name: 'text_promotion_2nd',
      desc: '',
      args: [],
    );
  }

  /// `Enter promotional code`
  String get text_enter_promotional_code {
    return Intl.message(
      'Enter promotional code',
      name: 'text_enter_promotional_code',
      desc: '',
      args: [],
    );
  }

  /// `You are having {content} promotional code, please enjoy`
  String text_you_have_promotional_code(String content) {
    return Intl.message(
      'You are having $content promotional code, please enjoy',
      name: 'text_you_have_promotional_code',
      desc: '',
      args: [content],
    );
  }

  /// `Booking code: {content}`
  String text_booking_code(String content) {
    return Intl.message(
      'Booking code: $content',
      name: 'text_booking_code',
      desc: '',
      args: [content],
    );
  }

  /// `Payment method`
  String get text_payment_method {
    return Intl.message(
      'Payment method',
      name: 'text_payment_method',
      desc: '',
      args: [],
    );
  }

  /// `By clicking the Pay button, you confirm that I have agreed to `
  String get text_by_clicking_pay_button {
    return Intl.message(
      'By clicking the Pay button, you confirm that I have agreed to ',
      name: 'text_by_clicking_pay_button',
      desc: '',
      args: [],
    );
  }

  /// `Terms of Use`
  String get text_terms_of_use {
    return Intl.message(
      'Terms of Use',
      name: 'text_terms_of_use',
      desc: '',
      args: [],
    );
  }

  /// `Conditions and Privacy Policy`
  String get text_conditions_and_privacy {
    return Intl.message(
      'Conditions and Privacy Policy',
      name: 'text_conditions_and_privacy',
      desc: '',
      args: [],
    );
  }

  /// ` of TripC`
  String get text_of_tripc {
    return Intl.message(
      ' of TripC',
      name: 'text_of_tripc',
      desc: '',
      args: [],
    );
  }

  /// `View placed order`
  String get text_view_booked_tours {
    return Intl.message(
      'View placed order',
      name: 'text_view_booked_tours',
      desc: '',
      args: [],
    );
  }

  /// `Back to Home Page`
  String get text_back_to_home {
    return Intl.message(
      'Back to Home Page',
      name: 'text_back_to_home',
      desc: '',
      args: [],
    );
  }

  /// `Payment successful!`
  String get text_payment_successful {
    return Intl.message(
      'Payment successful!',
      name: 'text_payment_successful',
      desc: '',
      args: [],
    );
  }

  /// `Your order has been successful. We wish you a safe and enjoyable journey.`
  String get text_dont_forget_share_moment {
    return Intl.message(
      'Your order has been successful. We wish you a safe and enjoyable journey.',
      name: 'text_dont_forget_share_moment',
      desc: '',
      args: [],
    );
  }

  /// `Don't forget to share those moments at TripC to receive incentives!`
  String get text_dont_forget_share_moment_2 {
    return Intl.message(
      'Don\'t forget to share those moments at TripC to receive incentives!',
      name: 'text_dont_forget_share_moment_2',
      desc: '',
      args: [],
    );
  }

  /// `Wishing you a journey full of interesting and safe experiences\nDon't forget, share those moments at TripC to receive incentives.`
  String get text_wishing {
    return Intl.message(
      'Wishing you a journey full of interesting and safe experiences\nDon\'t forget, share those moments at TripC to receive incentives.',
      name: 'text_wishing',
      desc: '',
      args: [],
    );
  }

  /// `Select promotional code`
  String get text_select_promotional_code {
    return Intl.message(
      'Select promotional code',
      name: 'text_select_promotional_code',
      desc: '',
      args: [],
    );
  }

  /// `Apply`
  String get text_apply {
    return Intl.message(
      'Apply',
      name: 'text_apply',
      desc: '',
      args: [],
    );
  }

  /// `There are currently no promotional codes available. If you have a promotional code, you can and apply it`
  String get text_no_promotional_code {
    return Intl.message(
      'There are currently no promotional codes available. If you have a promotional code, you can and apply it',
      name: 'text_no_promotional_code',
      desc: '',
      args: [],
    );
  }

  /// `No tours saved.`
  String get text_no_saved_tour {
    return Intl.message(
      'No tours saved.',
      name: 'text_no_saved_tour',
      desc: '',
      args: [],
    );
  }

  /// `International payment card (Visa/Master)`
  String get text_credit_card_method {
    return Intl.message(
      'International payment card (Visa/Master)',
      name: 'text_credit_card_method',
      desc: '',
      args: [],
    );
  }

  /// `TCent point`
  String get text_tcent_point_method {
    return Intl.message(
      'TCent point',
      name: 'text_tcent_point_method',
      desc: '',
      args: [],
    );
  }

  /// `VietQR (TCent refund up to 10% value)`
  String get text_vietqr_method {
    return Intl.message(
      'VietQR (TCent refund up to 10% value)',
      name: 'text_vietqr_method',
      desc: '',
      args: [],
    );
  }

  /// `Momo e-wallet (TCent refund up to 10% value)`
  String get text_momo_method {
    return Intl.message(
      'Momo e-wallet (TCent refund up to 10% value)',
      name: 'text_momo_method',
      desc: '',
      args: [],
    );
  }

  /// `ZaloPay e-wallet (TCent refund up to 10% value)`
  String get text_zalo_pay_method {
    return Intl.message(
      'ZaloPay e-wallet (TCent refund up to 10% value)',
      name: 'text_zalo_pay_method',
      desc: '',
      args: [],
    );
  }

  /// `Waiting to scan payment QRCode`
  String get text_waiting_scan_qr {
    return Intl.message(
      'Waiting to scan payment QRCode',
      name: 'text_waiting_scan_qr',
      desc: '',
      args: [],
    );
  }

  /// `Received {content}`
  String text_received_tcent(String content) {
    return Intl.message(
      'Received $content',
      name: 'text_received_tcent',
      desc: '',
      args: [content],
    );
  }

  /// `Tet sale 10% TCent refund`
  String get text_sale_refund_10 {
    return Intl.message(
      'Tet sale 10% TCent refund',
      name: 'text_sale_refund_10',
      desc: '',
      args: [],
    );
  }

  /// `Not yet paid`
  String get text_not_yet_paid {
    return Intl.message(
      'Not yet paid',
      name: 'text_not_yet_paid',
      desc: '',
      args: [],
    );
  }

  /// `Paid`
  String get text_paid {
    return Intl.message(
      'Paid',
      name: 'text_paid',
      desc: '',
      args: [],
    );
  }

  /// `Recently viewed`
  String get text_recently_viewed {
    return Intl.message(
      'Recently viewed',
      name: 'text_recently_viewed',
      desc: '',
      args: [],
    );
  }

  /// `Booking code`
  String get text_booking_code_title {
    return Intl.message(
      'Booking code',
      name: 'text_booking_code_title',
      desc: '',
      args: [],
    );
  }

  /// `Waiting for payment`
  String get text_waiting_for_payment {
    return Intl.message(
      'Waiting for payment',
      name: 'text_waiting_for_payment',
      desc: '',
      args: [],
    );
  }

  /// `Payment has been successful`
  String get text_payment_has_been_successful {
    return Intl.message(
      'Payment has been successful',
      name: 'text_payment_has_been_successful',
      desc: '',
      args: [],
    );
  }

  /// `Do not have`
  String get text_do_not_have {
    return Intl.message(
      'Do not have',
      name: 'text_do_not_have',
      desc: '',
      args: [],
    );
  }

  /// `Reservation details`
  String get text_reservation_details {
    return Intl.message(
      'Reservation details',
      name: 'text_reservation_details',
      desc: '',
      args: [],
    );
  }

  /// `Reset`
  String get text_reset {
    return Intl.message(
      'Reset',
      name: 'text_reset',
      desc: '',
      args: [],
    );
  }

  /// `Booking date:`
  String get text_booking_date_time {
    return Intl.message(
      'Booking date:',
      name: 'text_booking_date_time',
      desc: '',
      args: [],
    );
  }

  /// `Price`
  String get text_price {
    return Intl.message(
      'Price',
      name: 'text_price',
      desc: '',
      args: [],
    );
  }

  /// `Status`
  String get text_status {
    return Intl.message(
      'Status',
      name: 'text_status',
      desc: '',
      args: [],
    );
  }

  /// `Transaction code`
  String get text_transaction_code {
    return Intl.message(
      'Transaction code',
      name: 'text_transaction_code',
      desc: '',
      args: [],
    );
  }

  /// `Service information`
  String get text_service_information {
    return Intl.message(
      'Service information',
      name: 'text_service_information',
      desc: '',
      args: [],
    );
  }

  /// `Serivce's name:`
  String get text_service_name {
    return Intl.message(
      'Serivce\'s name:',
      name: 'text_service_name',
      desc: '',
      args: [],
    );
  }

  /// `Quantity:`
  String get text_quantity {
    return Intl.message(
      'Quantity:',
      name: 'text_quantity',
      desc: '',
      args: [],
    );
  }

  /// `Passenger`
  String get text_passenger {
    return Intl.message(
      'Passenger',
      name: 'text_passenger',
      desc: '',
      args: [],
    );
  }

  /// `Download ticket`
  String get text_download_ticket {
    return Intl.message(
      'Download ticket',
      name: 'text_download_ticket',
      desc: '',
      args: [],
    );
  }

  /// `Discount`
  String get text_discount {
    return Intl.message(
      'Discount',
      name: 'text_discount',
      desc: '',
      args: [],
    );
  }

  /// `Reduce`
  String get text_reduce {
    return Intl.message(
      'Reduce',
      name: 'text_reduce',
      desc: '',
      args: [],
    );
  }

  /// `Save up to`
  String get text_save_up_to {
    return Intl.message(
      'Save up to',
      name: 'text_save_up_to',
      desc: '',
      args: [],
    );
  }

  /// `Validity period:`
  String get text_validity_period {
    return Intl.message(
      'Validity period:',
      name: 'text_validity_period',
      desc: '',
      args: [],
    );
  }

  /// `Receive`
  String get text_receive {
    return Intl.message(
      'Receive',
      name: 'text_receive',
      desc: '',
      args: [],
    );
  }

  /// `Train Ticket`
  String get text_train_ticket {
    return Intl.message(
      'Train Ticket',
      name: 'text_train_ticket',
      desc: '',
      args: [],
    );
  }

  /// `Phone number:`
  String get text_phone_number {
    return Intl.message(
      'Phone number:',
      name: 'text_phone_number',
      desc: '',
      args: [],
    );
  }

  /// `Enter contact phone number`
  String get text_enter_contact_phone_num {
    return Intl.message(
      'Enter contact phone number',
      name: 'text_enter_contact_phone_num',
      desc: '',
      args: [],
    );
  }

  /// `Enter fullname`
  String get text_enter_fullname {
    return Intl.message(
      'Enter fullname',
      name: 'text_enter_fullname',
      desc: '',
      args: [],
    );
  }

  /// `Best-selling tour`
  String get text_best_selling {
    return Intl.message(
      'Best-selling tour',
      name: 'text_best_selling',
      desc: '',
      args: [],
    );
  }

  /// `Deeply discounted tour`
  String get text_deeply_discounted_tour {
    return Intl.message(
      'Deeply discounted tour',
      name: 'text_deeply_discounted_tour',
      desc: '',
      args: [],
    );
  }

  /// `Buy more and get big rewards`
  String get text_buy_more_get_rewards {
    return Intl.message(
      'Buy more and get big rewards',
      name: 'text_buy_more_get_rewards',
      desc: '',
      args: [],
    );
  }

  /// `Exclusively for you`
  String get text_exclusively_for_you {
    return Intl.message(
      'Exclusively for you',
      name: 'text_exclusively_for_you',
      desc: '',
      args: [],
    );
  }

  /// `All`
  String get text_all {
    return Intl.message(
      'All',
      name: 'text_all',
      desc: '',
      args: [],
    );
  }

  /// `Tour ticket`
  String get text_tour_ticket {
    return Intl.message(
      'Tour ticket',
      name: 'text_tour_ticket',
      desc: '',
      args: [],
    );
  }

  /// `Combo`
  String get text_combo {
    return Intl.message(
      'Combo',
      name: 'text_combo',
      desc: '',
      args: [],
    );
  }

  /// `Suggested keywords`
  String get text_suggested_keywords {
    return Intl.message(
      'Suggested keywords',
      name: 'text_suggested_keywords',
      desc: '',
      args: [],
    );
  }

  /// `Recent Searches`
  String get text_recent_searches {
    return Intl.message(
      'Recent Searches',
      name: 'text_recent_searches',
      desc: '',
      args: [],
    );
  }

  /// `Recommended Destination`
  String get text_recommended_destination {
    return Intl.message(
      'Recommended Destination',
      name: 'text_recommended_destination',
      desc: '',
      args: [],
    );
  }

  /// `Use now`
  String get text_use_now {
    return Intl.message(
      'Use now',
      name: 'text_use_now',
      desc: '',
      args: [],
    );
  }

  /// `Wishing you a journey full of fun and safe experiences\nCombo has an expiration date so remember to use it.\nDon't forget, share those moments at TripC to receive incentives.`
  String get text_wish_after_paid_combo {
    return Intl.message(
      'Wishing you a journey full of fun and safe experiences\nCombo has an expiration date so remember to use it.\nDon\'t forget, share those moments at TripC to receive incentives.',
      name: 'text_wish_after_paid_combo',
      desc: '',
      args: [],
    );
  }

  /// `Super cheap combo`
  String get text_super_cheap_combo {
    return Intl.message(
      'Super cheap combo',
      name: 'text_super_cheap_combo',
      desc: '',
      args: [],
    );
  }

  /// `Super cheap tour`
  String get text_super_cheap_tour {
    return Intl.message(
      'Super cheap tour',
      name: 'text_super_cheap_tour',
      desc: '',
      args: [],
    );
  }

  /// `Family combo`
  String get text_family_combo {
    return Intl.message(
      'Family combo',
      name: 'text_family_combo',
      desc: '',
      args: [],
    );
  }

  /// `Favorite combo`
  String get text_favorite_combo {
    return Intl.message(
      'Favorite combo',
      name: 'text_favorite_combo',
      desc: '',
      args: [],
    );
  }

  /// `Details included:`
  String get text_details_included {
    return Intl.message(
      'Details included:',
      name: 'text_details_included',
      desc: '',
      args: [],
    );
  }

  /// `Special:`
  String get text_special {
    return Intl.message(
      'Special:',
      name: 'text_special',
      desc: '',
      args: [],
    );
  }

  /// `Application period:`
  String get text_application_period {
    return Intl.message(
      'Application period:',
      name: 'text_application_period',
      desc: '',
      args: [],
    );
  }

  /// `Note:`
  String get text_note {
    return Intl.message(
      'Note:',
      name: 'text_note',
      desc: '',
      args: [],
    );
  }

  /// `Note:`
  String get text_note_2 {
    return Intl.message(
      'Note:',
      name: 'text_note_2',
      desc: '',
      args: [],
    );
  }

  /// `Note`
  String get text_note_label {
    return Intl.message(
      'Note',
      name: 'text_note_label',
      desc: '',
      args: [],
    );
  }

  /// `{content} days left`
  String text_days_left(int content) {
    return Intl.message(
      '$content days left',
      name: 'text_days_left',
      desc: '',
      args: [content],
    );
  }

  /// `You are having: `
  String get text_you_are_having {
    return Intl.message(
      'You are having: ',
      name: 'text_you_are_having',
      desc: '',
      args: [],
    );
  }

  /// `Manage My Account`
  String get text_my_account {
    return Intl.message(
      'Manage My Account',
      name: 'text_my_account',
      desc: '',
      args: [],
    );
  }

  /// `Hotel Membership Benefits`
  String get text_profile_holtel_membership {
    return Intl.message(
      'Hotel Membership Benefits',
      name: 'text_profile_holtel_membership',
      desc: '',
      args: [],
    );
  }

  /// `Name:`
  String get text_card_name_label {
    return Intl.message(
      'Name:',
      name: 'text_card_name_label',
      desc: '',
      args: [],
    );
  }

  /// `Exp:`
  String get text_card_exp_label {
    return Intl.message(
      'Exp:',
      name: 'text_card_exp_label',
      desc: '',
      args: [],
    );
  }

  /// `Balance amount:`
  String get text_card_balance_account {
    return Intl.message(
      'Balance amount:',
      name: 'text_card_balance_account',
      desc: '',
      args: [],
    );
  }

  /// `Use TCent to save more`
  String get text_tcent_hint {
    return Intl.message(
      'Use TCent to save more',
      name: 'text_tcent_hint',
      desc: '',
      args: [],
    );
  }

  /// `Promo Code`
  String get text_profile_promotion {
    return Intl.message(
      'Promo Code',
      name: 'text_profile_promotion',
      desc: '',
      args: [],
    );
  }

  /// `Booked Tours`
  String get text_profile_tour_booked {
    return Intl.message(
      'Booked Tours',
      name: 'text_profile_tour_booked',
      desc: '',
      args: [],
    );
  }

  /// `Saved Tours`
  String get text_profile_tour_saved {
    return Intl.message(
      'Saved Tours',
      name: 'text_profile_tour_saved',
      desc: '',
      args: [],
    );
  }

  /// `My TripC ID`
  String get text_profile_wallet {
    return Intl.message(
      'My TripC ID',
      name: 'text_profile_wallet',
      desc: '',
      args: [],
    );
  }

  /// `Moments & \nReviews`
  String get text_profile_moment {
    return Intl.message(
      'Moments & \nReviews',
      name: 'text_profile_moment',
      desc: '',
      args: [],
    );
  }

  /// `Recently \nViewed`
  String get text_profile_recent {
    return Intl.message(
      'Recently \nViewed',
      name: 'text_profile_recent',
      desc: '',
      args: [],
    );
  }

  /// `Contact`
  String get text_profile_contact {
    return Intl.message(
      'Contact',
      name: 'text_profile_contact',
      desc: '',
      args: [],
    );
  }

  /// `Gifts`
  String get text_profile_gift {
    return Intl.message(
      'Gifts',
      name: 'text_profile_gift',
      desc: '',
      args: [],
    );
  }

  /// `Customer Support`
  String get text_profile_support {
    return Intl.message(
      'Customer Support',
      name: 'text_profile_support',
      desc: '',
      args: [],
    );
  }

  /// `About \nTripC.AI`
  String get text_profile_info {
    return Intl.message(
      'About \nTripC.AI',
      name: 'text_profile_info',
      desc: '',
      args: [],
    );
  }

  /// `Rate App`
  String get text_profile_review {
    return Intl.message(
      'Rate App',
      name: 'text_profile_review',
      desc: '',
      args: [],
    );
  }

  /// `Terms \n& Conditions`
  String get text_profile_pravicy {
    return Intl.message(
      'Terms \n& Conditions',
      name: 'text_profile_pravicy',
      desc: '',
      args: [],
    );
  }

  /// `Male`
  String get text_male {
    return Intl.message(
      'Male',
      name: 'text_male',
      desc: '',
      args: [],
    );
  }

  /// `Female`
  String get text_female {
    return Intl.message(
      'Female',
      name: 'text_female',
      desc: '',
      args: [],
    );
  }

  /// `Don't want to reveal`
  String get text_other_gender {
    return Intl.message(
      'Don\'t want to reveal',
      name: 'text_other_gender',
      desc: '',
      args: [],
    );
  }

  /// `Personal Information`
  String get text_personal_info {
    return Intl.message(
      'Personal Information',
      name: 'text_personal_info',
      desc: '',
      args: [],
    );
  }

  /// `Edit`
  String get text_edit {
    return Intl.message(
      'Edit',
      name: 'text_edit',
      desc: '',
      args: [],
    );
  }

  /// `Display name`
  String get text_display_name {
    return Intl.message(
      'Display name',
      name: 'text_display_name',
      desc: '',
      args: [],
    );
  }

  /// `Gender`
  String get text_gender {
    return Intl.message(
      'Gender',
      name: 'text_gender',
      desc: '',
      args: [],
    );
  }

  /// `National`
  String get text_national {
    return Intl.message(
      'National',
      name: 'text_national',
      desc: '',
      args: [],
    );
  }

  /// `City of residence`
  String get text_city_of_residence {
    return Intl.message(
      'City of residence',
      name: 'text_city_of_residence',
      desc: '',
      args: [],
    );
  }

  /// `Account Settings`
  String get text_account_settings {
    return Intl.message(
      'Account Settings',
      name: 'text_account_settings',
      desc: '',
      args: [],
    );
  }

  /// `Linked email`
  String get text_linked_email {
    return Intl.message(
      'Linked email',
      name: 'text_linked_email',
      desc: '',
      args: [],
    );
  }

  /// `Phone number`
  String get text_linked_phone {
    return Intl.message(
      'Phone number',
      name: 'text_linked_phone',
      desc: '',
      args: [],
    );
  }

  /// `Link`
  String get text_link {
    return Intl.message(
      'Link',
      name: 'text_link',
      desc: '',
      args: [],
    );
  }

  /// `Reset password`
  String get text_reset_password {
    return Intl.message(
      'Reset password',
      name: 'text_reset_password',
      desc: '',
      args: [],
    );
  }

  /// `Unlink`
  String get text_unlink {
    return Intl.message(
      'Unlink',
      name: 'text_unlink',
      desc: '',
      args: [],
    );
  }

  /// `Account linking`
  String get text_account_linking {
    return Intl.message(
      'Account linking',
      name: 'text_account_linking',
      desc: '',
      args: [],
    );
  }

  /// `Account linking allows you to quickly log in to TripC`
  String get text_account_linking_description {
    return Intl.message(
      'Account linking allows you to quickly log in to TripC',
      name: 'text_account_linking_description',
      desc: '',
      args: [],
    );
  }

  /// `Log out`
  String get text_log_out {
    return Intl.message(
      'Log out',
      name: 'text_log_out',
      desc: '',
      args: [],
    );
  }

  /// `Delete My Account`
  String get text_delete_my_account {
    return Intl.message(
      'Delete My Account',
      name: 'text_delete_my_account',
      desc: '',
      args: [],
    );
  }

  /// `If your account is deleted, all account information will also be deleted.\nYou will not be able to recover this information.`
  String get text_delete_my_account_note {
    return Intl.message(
      'If your account is deleted, all account information will also be deleted.\nYou will not be able to recover this information.',
      name: 'text_delete_my_account_note',
      desc: '',
      args: [],
    );
  }

  /// `As you know, when you delete your account`
  String get text_as_u_know_delete_account {
    return Intl.message(
      'As you know, when you delete your account',
      name: 'text_as_u_know_delete_account',
      desc: '',
      args: [],
    );
  }

  /// `You will not be able to check past bookings.`
  String get text_delete_account_dialog_note_1 {
    return Intl.message(
      'You will not be able to check past bookings.',
      name: 'text_delete_account_dialog_note_1',
      desc: '',
      args: [],
    );
  }

  /// `You won't be able to sign in to your account.`
  String get text_delete_account_dialog_note_2 {
    return Intl.message(
      'You won\'t be able to sign in to your account.',
      name: 'text_delete_account_dialog_note_2',
      desc: '',
      args: [],
    );
  }

  /// `You will lose all TCent and discount codes.`
  String get text_delete_account_dialog_note_3 {
    return Intl.message(
      'You will lose all TCent and discount codes.',
      name: 'text_delete_account_dialog_note_3',
      desc: '',
      args: [],
    );
  }

  /// `We will not assist in recovering your account.`
  String get text_delete_account_dialog_note_4 {
    return Intl.message(
      'We will not assist in recovering your account.',
      name: 'text_delete_account_dialog_note_4',
      desc: '',
      args: [],
    );
  }

  /// `If you still want to delete your account, please ensure that all bookings are completed and that you have no issues or concerns after deleting your account.`
  String get text_delete_account_dialog_note_5 {
    return Intl.message(
      'If you still want to delete your account, please ensure that all bookings are completed and that you have no issues or concerns after deleting your account.',
      name: 'text_delete_account_dialog_note_5',
      desc: '',
      args: [],
    );
  }

  /// `Send`
  String get text_send {
    return Intl.message(
      'Send',
      name: 'text_send',
      desc: '',
      args: [],
    );
  }

  /// `TripC ID`
  String get text_tripcID {
    return Intl.message(
      'TripC ID',
      name: 'text_tripcID',
      desc: '',
      args: [],
    );
  }

  /// `Enter email to get passcode`
  String get text_enter_email_to_get_passcode {
    return Intl.message(
      'Enter email to get passcode',
      name: 'text_enter_email_to_get_passcode',
      desc: '',
      args: [],
    );
  }

  /// `Feature under development\nWill be released as soon as possible`
  String get text_comming_soon_text {
    return Intl.message(
      'Feature under development\nWill be released as soon as possible',
      name: 'text_comming_soon_text',
      desc: '',
      args: [],
    );
  }

  /// `Membership Offers`
  String get text_hotel_membership_offers {
    return Intl.message(
      'Membership Offers',
      name: 'text_hotel_membership_offers',
      desc: '',
      args: [],
    );
  }

  /// `See more`
  String get text_see_more {
    return Intl.message(
      'See more',
      name: 'text_see_more',
      desc: '',
      args: [],
    );
  }

  /// `Big deals`
  String get text_big_deals {
    return Intl.message(
      'Big deals',
      name: 'text_big_deals',
      desc: '',
      args: [],
    );
  }

  /// `Tu quy number`
  String get text_tu_quy_number {
    return Intl.message(
      'Tu quy number',
      name: 'text_tu_quy_number',
      desc: '',
      args: [],
    );
  }

  /// `Wealthy number`
  String get text_wealthy_number {
    return Intl.message(
      'Wealthy number',
      name: 'text_wealthy_number',
      desc: '',
      args: [],
    );
  }

  /// `Great peace number`
  String get text_great_peace_number {
    return Intl.message(
      'Great peace number',
      name: 'text_great_peace_number',
      desc: '',
      args: [],
    );
  }

  /// `Meaning of luck, money and prosperity. For example: 68, 86, 39, 79,...`
  String get text_proposer_number_meaning {
    return Intl.message(
      'Meaning of luck, money and prosperity. For example: 68, 86, 39, 79,...',
      name: 'text_proposer_number_meaning',
      desc: '',
      args: [],
    );
  }

  /// `Consists of 4 consecutive identical numbers (1111, 2222, 3333,...) representing solidity, sustainability and class.`
  String get text_tu_quy_number_meaning {
    return Intl.message(
      'Consists of 4 consecutive identical numbers (1111, 2222, 3333,...) representing solidity, sustainability and class.',
      name: 'text_tu_quy_number_meaning',
      desc: '',
      args: [],
    );
  }

  /// `Express wealth and prosperity with numbers that have good meaning\nFor example: 6886, 8386, 9999.`
  String get text_wealthy_number_meaning {
    return Intl.message(
      'Express wealth and prosperity with numbers that have good meaning\nFor example: 6886, 8386, 9999.',
      name: 'text_wealthy_number_meaning',
      desc: '',
      args: [],
    );
  }

  /// `Symbol of harmony, peace and stable development, suitable for those who want a peaceful life.`
  String get text_great_peace_number_meaning {
    return Intl.message(
      'Symbol of harmony, peace and stable development, suitable for those who want a peaceful life.',
      name: 'text_great_peace_number_meaning',
      desc: '',
      args: [],
    );
  }

  /// `As you know, when you remove TripC ID`
  String get text_as_u_know_remove_tripcid {
    return Intl.message(
      'As you know, when you remove TripC ID',
      name: 'text_as_u_know_remove_tripcid',
      desc: '',
      args: [],
    );
  }

  /// `You will not be able to recover your TripC ID Number`
  String get text_remove_tripcid_dialog_note_1 {
    return Intl.message(
      'You will not be able to recover your TripC ID Number',
      name: 'text_remove_tripcid_dialog_note_1',
      desc: '',
      args: [],
    );
  }

  /// `We will issue this TripC ID to another account`
  String get text_remove_tripcid_dialog_note_2 {
    return Intl.message(
      'We will issue this TripC ID to another account',
      name: 'text_remove_tripcid_dialog_note_2',
      desc: '',
      args: [],
    );
  }

  /// `Reset Passcode`
  String get text_reset_passcode {
    return Intl.message(
      'Reset Passcode',
      name: 'text_reset_passcode',
      desc: '',
      args: [],
    );
  }

  /// `Set as default`
  String get text_set_as_default {
    return Intl.message(
      'Set as default',
      name: 'text_set_as_default',
      desc: '',
      args: [],
    );
  }

  /// `Remove TripC ID from your account`
  String get text_remove_tripc_id {
    return Intl.message(
      'Remove TripC ID from your account',
      name: 'text_remove_tripc_id',
      desc: '',
      args: [],
    );
  }

  /// `TripC ID's setting`
  String get text_tripc_id_setting {
    return Intl.message(
      'TripC ID\'s setting',
      name: 'text_tripc_id_setting',
      desc: '',
      args: [],
    );
  }

  /// `TripC ID list`
  String get text_tripc_id_list {
    return Intl.message(
      'TripC ID list',
      name: 'text_tripc_id_list',
      desc: '',
      args: [],
    );
  }

  /// `Add New TripC ID`
  String get text_add_new_tripc_id {
    return Intl.message(
      'Add New TripC ID',
      name: 'text_add_new_tripc_id',
      desc: '',
      args: [],
    );
  }

  /// `Error`
  String get text_error {
    return Intl.message(
      'Error',
      name: 'text_error',
      desc: '',
      args: [],
    );
  }

  /// `Enter Passcode`
  String get text_enter_passcode {
    return Intl.message(
      'Enter Passcode',
      name: 'text_enter_passcode',
      desc: '',
      args: [],
    );
  }

  /// `Passcode is incorrect. If you enter the wrong password more than 5 times your password will be locked for 24 hours.`
  String get text_warning_type_passcode {
    return Intl.message(
      'Passcode is incorrect. If you enter the wrong password more than 5 times your password will be locked for 24 hours.',
      name: 'text_warning_type_passcode',
      desc: '',
      args: [],
    );
  }

  /// `You have entered the wrong passcode more than 5 times and are locked for 24 hours.`
  String get text_block_type_passcode {
    return Intl.message(
      'You have entered the wrong passcode more than 5 times and are locked for 24 hours.',
      name: 'text_block_type_passcode',
      desc: '',
      args: [],
    );
  }

  /// `You have`
  String get text_you_have {
    return Intl.message(
      'You have',
      name: 'text_you_have',
      desc: '',
      args: [],
    );
  }

  /// `try times`
  String get text_try_times {
    return Intl.message(
      'try times',
      name: 'text_try_times',
      desc: '',
      args: [],
    );
  }

  /// `Passcode is used when using services and TripC ID cards.\nPlease avoid numbers related to birthdays, numbers that are too easy to guess.`
  String get text_reset_passcode_note {
    return Intl.message(
      'Passcode is used when using services and TripC ID cards.\nPlease avoid numbers related to birthdays, numbers that are too easy to guess.',
      name: 'text_reset_passcode_note',
      desc: '',
      args: [],
    );
  }

  /// `Enter new passcode`
  String get text_enter_new_passcode {
    return Intl.message(
      'Enter new passcode',
      name: 'text_enter_new_passcode',
      desc: '',
      args: [],
    );
  }

  /// `Re-enter new passcode`
  String get text_re_enter_new_passcode {
    return Intl.message(
      'Re-enter new passcode',
      name: 'text_re_enter_new_passcode',
      desc: '',
      args: [],
    );
  }

  /// `Passcode reset successfully`
  String get text_passcode_reset_successfully {
    return Intl.message(
      'Passcode reset successfully',
      name: 'text_passcode_reset_successfully',
      desc: '',
      args: [],
    );
  }

  /// `You have successfully reset your Passcode. Use the reset Passcode for the next times.`
  String get text_you_have_successfully_set_pass {
    return Intl.message(
      'You have successfully reset your Passcode. Use the reset Passcode for the next times.',
      name: 'text_you_have_successfully_set_pass',
      desc: '',
      args: [],
    );
  }

  /// `Use the password you just set for future accesses.`
  String get text_use_the_password_you_just_set {
    return Intl.message(
      'Use the password you just set for future accesses.',
      name: 'text_use_the_password_you_just_set',
      desc: '',
      args: [],
    );
  }

  /// `Passcode re-entered incorrectly`
  String get text_re_entered_new_passcode_incorrectly {
    return Intl.message(
      'Passcode re-entered incorrectly',
      name: 'text_re_entered_new_passcode_incorrectly',
      desc: '',
      args: [],
    );
  }

  /// `OTP Code Verification`
  String get text_otp_code_verification {
    return Intl.message(
      'OTP Code Verification',
      name: 'text_otp_code_verification',
      desc: '',
      args: [],
    );
  }

  /// `We've sent you a 6-digit code to your email`
  String get text_sent_6_digit_code {
    return Intl.message(
      'We\'ve sent you a 6-digit code to your email',
      name: 'text_sent_6_digit_code',
      desc: '',
      args: [],
    );
  }

  /// `Verify`
  String get text_verify {
    return Intl.message(
      'Verify',
      name: 'text_verify',
      desc: '',
      args: [],
    );
  }

  /// `You didn't receive the OTP code?`
  String get text_did_not_receive_otp {
    return Intl.message(
      'You didn\'t receive the OTP code?',
      name: 'text_did_not_receive_otp',
      desc: '',
      args: [],
    );
  }

  /// `Resend OTP code`
  String get text_resend_otp {
    return Intl.message(
      'Resend OTP code',
      name: 'text_resend_otp',
      desc: '',
      args: [],
    );
  }

  /// `Please set a more secure password for your account.\nPlease avoid numbers related to birthdays, which are too easy to guess.`
  String get text_pls_set_security_pass {
    return Intl.message(
      'Please set a more secure password for your account.\nPlease avoid numbers related to birthdays, which are too easy to guess.',
      name: 'text_pls_set_security_pass',
      desc: '',
      args: [],
    );
  }

  /// `New Password`
  String get text_enter_new_pass {
    return Intl.message(
      'New Password',
      name: 'text_enter_new_pass',
      desc: '',
      args: [],
    );
  }

  /// `Confirm New Password`
  String get text_re_enter_new_pass {
    return Intl.message(
      'Confirm New Password',
      name: 'text_re_enter_new_pass',
      desc: '',
      args: [],
    );
  }

  /// `Re-enter the password`
  String get text_re_enter_password {
    return Intl.message(
      'Re-enter the password',
      name: 'text_re_enter_password',
      desc: '',
      args: [],
    );
  }

  /// `Password must be at least 8 characters long, contain uppercase letters, lowercase letters, numbers and special characters`
  String get text_password_verification {
    return Intl.message(
      'Password must be at least 8 characters long, contain uppercase letters, lowercase letters, numbers and special characters',
      name: 'text_password_verification',
      desc: '',
      args: [],
    );
  }

  /// `The newly entered password is incorrect`
  String get text_newly_entered_password_is_incorrect {
    return Intl.message(
      'The newly entered password is incorrect',
      name: 'text_newly_entered_password_is_incorrect',
      desc: '',
      args: [],
    );
  }

  /// `Password reset successfully`
  String get text_password_reset_successfully {
    return Intl.message(
      'Password reset successfully',
      name: 'text_password_reset_successfully',
      desc: '',
      args: [],
    );
  }

  /// `Membership rank`
  String get text_membership_rank {
    return Intl.message(
      'Membership rank',
      name: 'text_membership_rank',
      desc: '',
      args: [],
    );
  }

  /// `Login to see membership rank`
  String get text_login_to_see_membership_rank {
    return Intl.message(
      'Login to see membership rank',
      name: 'text_login_to_see_membership_rank',
      desc: '',
      args: [],
    );
  }

  /// `Oops! You are not logged in!`
  String get text_opp_not_logged_in {
    return Intl.message(
      'Oops! You are not logged in!',
      name: 'text_opp_not_logged_in',
      desc: '',
      args: [],
    );
  }

  /// `Sign in now!`
  String get text_sign_in_now {
    return Intl.message(
      'Sign in now!',
      name: 'text_sign_in_now',
      desc: '',
      args: [],
    );
  }

  /// `Please log in or register to explore content.`
  String get text_please_log_in_or_register {
    return Intl.message(
      'Please log in or register to explore content.',
      name: 'text_please_log_in_or_register',
      desc: '',
      args: [],
    );
  }

  /// `Sign in/ Sign up`
  String get text_sign_in_or_sign_up {
    return Intl.message(
      'Sign in/ Sign up',
      name: 'text_sign_in_or_sign_up',
      desc: '',
      args: [],
    );
  }

  /// `Messages`
  String get text_messages {
    return Intl.message(
      'Messages',
      name: 'text_messages',
      desc: '',
      args: [],
    );
  }

  /// `Select a nice account number`
  String get text_select_a_nice_account_number {
    return Intl.message(
      'Select a nice account number',
      name: 'text_select_a_nice_account_number',
      desc: '',
      args: [],
    );
  }

  /// `Vietnamese`
  String get text_vietnamese {
    return Intl.message(
      'Vietnamese',
      name: 'text_vietnamese',
      desc: '',
      args: [],
    );
  }

  /// `English`
  String get text_english {
    return Intl.message(
      'English',
      name: 'text_english',
      desc: '',
      args: [],
    );
  }

  /// `Language`
  String get text_language {
    return Intl.message(
      'Language',
      name: 'text_language',
      desc: '',
      args: [],
    );
  }

  /// `Setting`
  String get text_setting {
    return Intl.message(
      'Setting',
      name: 'text_setting',
      desc: '',
      args: [],
    );
  }

  /// `Currency unit`
  String get text_currency_unit {
    return Intl.message(
      'Currency unit',
      name: 'text_currency_unit',
      desc: '',
      args: [],
    );
  }

  /// `Account management`
  String get text_account_management {
    return Intl.message(
      'Account management',
      name: 'text_account_management',
      desc: '',
      args: [],
    );
  }

  /// `Dark mode`
  String get text_dark_mode {
    return Intl.message(
      'Dark mode',
      name: 'text_dark_mode',
      desc: '',
      args: [],
    );
  }

  /// `Terms & Conditions`
  String get text_terms_conditions {
    return Intl.message(
      'Terms & Conditions',
      name: 'text_terms_conditions',
      desc: '',
      args: [],
    );
  }

  /// `Privacy`
  String get text_privacy {
    return Intl.message(
      'Privacy',
      name: 'text_privacy',
      desc: '',
      args: [],
    );
  }

  /// `Version`
  String get text_version {
    return Intl.message(
      'Version',
      name: 'text_version',
      desc: '',
      args: [],
    );
  }

  /// `Add phone number`
  String get text_link_phone_number {
    return Intl.message(
      'Add phone number',
      name: 'text_link_phone_number',
      desc: '',
      args: [],
    );
  }

  /// `Mobile contact number`
  String get text_enter_phone_number {
    return Intl.message(
      'Mobile contact number',
      name: 'text_enter_phone_number',
      desc: '',
      args: [],
    );
  }

  /// `Please enter a valid email!`
  String get text_email_invalid {
    return Intl.message(
      'Please enter a valid email!',
      name: 'text_email_invalid',
      desc: '',
      args: [],
    );
  }

  /// `Password must be valid`
  String get text_password_invalid {
    return Intl.message(
      'Password must be valid',
      name: 'text_password_invalid',
      desc: '',
      args: [],
    );
  }

  /// `Please enter phone number`
  String get text_pls_type_phone_number {
    return Intl.message(
      'Please enter phone number',
      name: 'text_pls_type_phone_number',
      desc: '',
      args: [],
    );
  }

  /// `Please enter a valid phone number (9 digits)`
  String get text_pls_type_valid_phone_number {
    return Intl.message(
      'Please enter a valid phone number (9 digits)',
      name: 'text_pls_type_valid_phone_number',
      desc: '',
      args: [],
    );
  }

  /// `The new phone number must be different from the current phone number.`
  String get text_not_same_old_phone_number {
    return Intl.message(
      'The new phone number must be different from the current phone number.',
      name: 'text_not_same_old_phone_number',
      desc: '',
      args: [],
    );
  }

  /// `Sliver Tier`
  String get text_sliver_tier {
    return Intl.message(
      'Sliver Tier',
      name: 'text_sliver_tier',
      desc: '',
      args: [],
    );
  }

  /// `Gold Tier`
  String get text_gold_tier {
    return Intl.message(
      'Gold Tier',
      name: 'text_gold_tier',
      desc: '',
      args: [],
    );
  }

  /// `Diamond Tier`
  String get text_diamond_tier {
    return Intl.message(
      'Diamond Tier',
      name: 'text_diamond_tier',
      desc: '',
      args: [],
    );
  }

  /// `Country or region`
  String get text_country_or_region {
    return Intl.message(
      'Country or region',
      name: 'text_country_or_region',
      desc: '',
      args: [],
    );
  }

  /// `The OTP code is sent back to your Email`
  String get text_otp_has_been_sent {
    return Intl.message(
      'The OTP code is sent back to your Email',
      name: 'text_otp_has_been_sent',
      desc: '',
      args: [],
    );
  }

  /// `Wrong confirmation code. Please try again!`
  String get text_wrong_otp {
    return Intl.message(
      'Wrong confirmation code. Please try again!',
      name: 'text_wrong_otp',
      desc: '',
      args: [],
    );
  }

  /// `The new email address must be different from the current email address.`
  String get text_same_old_email {
    return Intl.message(
      'The new email address must be different from the current email address.',
      name: 'text_same_old_email',
      desc: '',
      args: [],
    );
  }

  /// `We have sent the confirmation code to the address`
  String get text_we_have_sent_code_to_email {
    return Intl.message(
      'We have sent the confirmation code to the address',
      name: 'text_we_have_sent_code_to_email',
      desc: '',
      args: [],
    );
  }

  /// `Please check your inbox and enter the code below`
  String get text_pls_check_inbox_email {
    return Intl.message(
      'Please check your inbox and enter the code below',
      name: 'text_pls_check_inbox_email',
      desc: '',
      args: [],
    );
  }

  /// `* The confirmation code is valid for 30 minutes after you receive it.`
  String get text_otp_valid_30_minute {
    return Intl.message(
      '* The confirmation code is valid for 30 minutes after you receive it.',
      name: 'text_otp_valid_30_minute',
      desc: '',
      args: [],
    );
  }

  /// `Haven't received the code yet? `
  String get text_have_not_receive_otp {
    return Intl.message(
      'Haven\'t received the code yet? ',
      name: 'text_have_not_receive_otp',
      desc: '',
      args: [],
    );
  }

  /// `Resend`
  String get text_resend {
    return Intl.message(
      'Resend',
      name: 'text_resend',
      desc: '',
      args: [],
    );
  }

  /// `Please enter a new email address. Once successfully linked to your account, you can use this email to log in.`
  String get text_pls_enter_a_new_email {
    return Intl.message(
      'Please enter a new email address. Once successfully linked to your account, you can use this email to log in.',
      name: 'text_pls_enter_a_new_email',
      desc: '',
      args: [],
    );
  }

  /// `Email link`
  String get text_email_link {
    return Intl.message(
      'Email link',
      name: 'text_email_link',
      desc: '',
      args: [],
    );
  }

  /// `New email link`
  String get text_new_email_link {
    return Intl.message(
      'New email link',
      name: 'text_new_email_link',
      desc: '',
      args: [],
    );
  }

  /// `Send Confirmation Code`
  String get text_send_confirmation_code {
    return Intl.message(
      'Send Confirmation Code',
      name: 'text_send_confirmation_code',
      desc: '',
      args: [],
    );
  }

  /// `Continue`
  String get text_continue {
    return Intl.message(
      'Continue',
      name: 'text_continue',
      desc: '',
      args: [],
    );
  }

  /// `Change Email Link`
  String get text_change_email_link {
    return Intl.message(
      'Change Email Link',
      name: 'text_change_email_link',
      desc: '',
      args: [],
    );
  }

  /// `Confirmation code has been sent`
  String get text_confirmation_code_has_been_sent {
    return Intl.message(
      'Confirmation code has been sent',
      name: 'text_confirmation_code_has_been_sent',
      desc: '',
      args: [],
    );
  }

  /// `Your account is now associated with an email address. If you want to change to a new email address, you will need to verify your identity`
  String get text_warning_identify_email_link {
    return Intl.message(
      'Your account is now associated with an email address. If you want to change to a new email address, you will need to verify your identity',
      name: 'text_warning_identify_email_link',
      desc: '',
      args: [],
    );
  }

  /// `Sale`
  String get text_sale {
    return Intl.message(
      'Sale',
      name: 'text_sale',
      desc: '',
      args: [],
    );
  }

  /// `Flexible ticket`
  String get text_flexible_ticket {
    return Intl.message(
      'Flexible ticket',
      name: 'text_flexible_ticket',
      desc: '',
      args: [],
    );
  }

  /// `Products you have searched for`
  String get text_products_you_have_searched_for {
    return Intl.message(
      'Products you have searched for',
      name: 'text_products_you_have_searched_for',
      desc: '',
      args: [],
    );
  }

  /// `Invalid information. Please check your TripC ID and Passcode again.`
  String get text_wrong_tripcId_and_pass_code {
    return Intl.message(
      'Invalid information. Please check your TripC ID and Passcode again.',
      name: 'text_wrong_tripcId_and_pass_code',
      desc: '',
      args: [],
    );
  }

  /// `Successfully created membership card`
  String get text_successfully_created_membership_card {
    return Intl.message(
      'Successfully created membership card',
      name: 'text_successfully_created_membership_card',
      desc: '',
      args: [],
    );
  }

  /// `Congratulations, you have successfully created your membership card.\nExperience new features and incentives.`
  String get text_congratulation_create_membership_card {
    return Intl.message(
      'Congratulations, you have successfully created your membership card.\nExperience new features and incentives.',
      name: 'text_congratulation_create_membership_card',
      desc: '',
      args: [],
    );
  }

  /// `Please enter full name from 2 to 50 characters!`
  String get text_pls_enter_full_name {
    return Intl.message(
      'Please enter full name from 2 to 50 characters!',
      name: 'text_pls_enter_full_name',
      desc: '',
      args: [],
    );
  }

  /// `Full name is not valid. Please re-enter!`
  String get text_full_name_invalid {
    return Intl.message(
      'Full name is not valid. Please re-enter!',
      name: 'text_full_name_invalid',
      desc: '',
      args: [],
    );
  }

  /// `Please enter a valid email address!`
  String get text_pls_enter_valid_email {
    return Intl.message(
      'Please enter a valid email address!',
      name: 'text_pls_enter_valid_email',
      desc: '',
      args: [],
    );
  }

  /// `Please enter a verified email address!`
  String get text_pls_enter_verified_email {
    return Intl.message(
      'Please enter a verified email address!',
      name: 'text_pls_enter_verified_email',
      desc: '',
      args: [],
    );
  }

  /// `This email is already in use. Please log in to your account!`
  String get text_email_already_in_use {
    return Intl.message(
      'This email is already in use. Please log in to your account!',
      name: 'text_email_already_in_use',
      desc: '',
      args: [],
    );
  }

  /// `Password must be at least 8 characters long, contain uppercase letters, lowercase letters, numbers and special characters`
  String get text_password_validation {
    return Intl.message(
      'Password must be at least 8 characters long, contain uppercase letters, lowercase letters, numbers and special characters',
      name: 'text_password_validation',
      desc: '',
      args: [],
    );
  }

  /// `Email has not been used to register an account`
  String get text_email_has_not_been_registered {
    return Intl.message(
      'Email has not been used to register an account',
      name: 'text_email_has_not_been_registered',
      desc: '',
      args: [],
    );
  }

  /// `Wrong password`
  String get text_pls_enter_correct_password {
    return Intl.message(
      'Wrong password',
      name: 'text_pls_enter_correct_password',
      desc: '',
      args: [],
    );
  }

  /// `You've entered too many times, so we've temporarily locked your account.\nPlease try again after {content}s.`
  String text_warning_lock_sign_in(String content) {
    return Intl.message(
      'You\'ve entered too many times, so we\'ve temporarily locked your account.\nPlease try again after ${content}s.',
      name: 'text_warning_lock_sign_in',
      desc: '',
      args: [content],
    );
  }

  /// `Account is inactive`
  String get text_inactive_account {
    return Intl.message(
      'Account is inactive',
      name: 'text_inactive_account',
      desc: '',
      args: [],
    );
  }

  /// `discount code , enjoy`
  String get text_discount_code_enjoy {
    return Intl.message(
      'discount code , enjoy',
      name: 'text_discount_code_enjoy',
      desc: '',
      args: [],
    );
  }

  /// `Free cancellation`
  String get text_cancel_free {
    return Intl.message(
      'Free cancellation',
      name: 'text_cancel_free',
      desc: '',
      args: [],
    );
  }

  /// `Order Information`
  String get text_order_information {
    return Intl.message(
      'Order Information',
      name: 'text_order_information',
      desc: '',
      args: [],
    );
  }

  /// `Available Tomorrow`
  String get text_available_omorrow {
    return Intl.message(
      'Available Tomorrow',
      name: 'text_available_omorrow',
      desc: '',
      args: [],
    );
  }

  /// `Must be purchased before 6:00 p.m., 1 day before use (Local Time)`
  String get text_must_purchase_before_six {
    return Intl.message(
      'Must be purchased before 6:00 p.m., 1 day before use (Local Time)',
      name: 'text_must_purchase_before_six',
      desc: '',
      args: [],
    );
  }

  /// `Ticket Change is Not Required`
  String get text_ticket_change_is_not_required {
    return Intl.message(
      'Ticket Change is Not Required',
      name: 'text_ticket_change_is_not_required',
      desc: '',
      args: [],
    );
  }

  /// `You will need an “E-Ticket” to verify and use your booking`
  String get text_need_e_invoice {
    return Intl.message(
      'You will need an “E-Ticket” to verify and use your booking',
      name: 'text_need_e_invoice',
      desc: '',
      args: [],
    );
  }

  /// `Conditional Cancellation`
  String get text_conditional_cancellation {
    return Intl.message(
      'Conditional Cancellation',
      name: 'text_conditional_cancellation',
      desc: '',
      args: [],
    );
  }

  /// `Cancellation time`
  String get text_cancellation_time {
    return Intl.message(
      'Cancellation time',
      name: 'text_cancellation_time',
      desc: '',
      args: [],
    );
  }

  /// `Until 01:00, 1 day before the previous use date`
  String get text_day_before_the_previous_use_date {
    return Intl.message(
      'Until 01:00, 1 day before the previous use date',
      name: 'text_day_before_the_previous_use_date',
      desc: '',
      args: [],
    );
  }

  /// `Until 01:00, 1 day before the next use date`
  String get text_day_before_the_next_use_date {
    return Intl.message(
      'Until 01:00, 1 day before the next use date',
      name: 'text_day_before_the_next_use_date',
      desc: '',
      args: [],
    );
  }

  /// `Cannot be canceled once activated`
  String get text_cannot_be_canceled_one_actived {
    return Intl.message(
      'Cannot be canceled once activated',
      name: 'text_cannot_be_canceled_one_actived',
      desc: '',
      args: [],
    );
  }

  /// `If a discount is applied, the fee will be calculated as a proportion of the pre-discount price. This fee will not exceed the amount actually paid. The product does not support partial refunds`
  String get text_if_a_discount_is_applied {
    return Intl.message(
      'If a discount is applied, the fee will be calculated as a proportion of the pre-discount price. This fee will not exceed the amount actually paid. The product does not support partial refunds',
      name: 'text_if_a_discount_is_applied',
      desc: '',
      args: [],
    );
  }

  /// `Fee`
  String get text_fee_text {
    return Intl.message(
      'Fee',
      name: 'text_fee_text',
      desc: '',
      args: [],
    );
  }

  /// `Sign up now`
  String get text_sign_up_now {
    return Intl.message(
      'Sign up now',
      name: 'text_sign_up_now',
      desc: '',
      args: [],
    );
  }

  /// `Your account has been temporarily locked`
  String get text_been_term_locked {
    return Intl.message(
      'Your account has been temporarily locked',
      name: 'text_been_term_locked',
      desc: '',
      args: [],
    );
  }

  /// `Explore Việt Nam`
  String get text_explore_viet_nam {
    return Intl.message(
      'Explore Việt Nam',
      name: 'text_explore_viet_nam',
      desc: '',
      args: [],
    );
  }

  /// `Explore new places, cultures and experiences.\nUnlock the doors to adventure and wanderlust`
  String get text_explore_viet_nam_content {
    return Intl.message(
      'Explore new places, cultures and experiences.\nUnlock the doors to adventure and wanderlust',
      name: 'text_explore_viet_nam_content',
      desc: '',
      args: [],
    );
  }

  /// `Full name must not exceed 50 characters!`
  String get text_full_name_over_50 {
    return Intl.message(
      'Full name must not exceed 50 characters!',
      name: 'text_full_name_over_50',
      desc: '',
      args: [],
    );
  }

  /// `Full names cannot contain numbers`
  String get text_full_name_without_num {
    return Intl.message(
      'Full names cannot contain numbers',
      name: 'text_full_name_without_num',
      desc: '',
      args: [],
    );
  }

  /// `Please do not enter special characters!`
  String get text_full_name_without_special_sign {
    return Intl.message(
      'Please do not enter special characters!',
      name: 'text_full_name_without_special_sign',
      desc: '',
      args: [],
    );
  }

  /// `Do not enter spaces!`
  String get text_full_name_without_space {
    return Intl.message(
      'Do not enter spaces!',
      name: 'text_full_name_without_space',
      desc: '',
      args: [],
    );
  }

  /// `Enter the referral code from your friend to immediately receive a special gift from us.`
  String get text_enter_your_referral_code_from_friend {
    return Intl.message(
      'Enter the referral code from your friend to immediately receive a special gift from us.',
      name: 'text_enter_your_referral_code_from_friend',
      desc: '',
      args: [],
    );
  }

  /// `Congratulations on receiving the offer`
  String get text_congratulations_on_receiving_the_offer {
    return Intl.message(
      'Congratulations on receiving the offer',
      name: 'text_congratulations_on_receiving_the_offer',
      desc: '',
      args: [],
    );
  }

  /// `Valid referral code! The reward will be sent to you in your voucher warehouse upon completion.`
  String get text_valid_referral_code {
    return Intl.message(
      'Valid referral code! The reward will be sent to you in your voucher warehouse upon completion.',
      name: 'text_valid_referral_code',
      desc: '',
      args: [],
    );
  }

  /// `New deal`
  String get text_new_deal {
    return Intl.message(
      'New deal',
      name: 'text_new_deal',
      desc: '',
      args: [],
    );
  }

  /// `You have reached the bottom of the page.`
  String get text_at_last_page {
    return Intl.message(
      'You have reached the bottom of the page.',
      name: 'text_at_last_page',
      desc: '',
      args: [],
    );
  }

  /// `Shuttle service:`
  String get text_shuttle_service {
    return Intl.message(
      'Shuttle service:',
      name: 'text_shuttle_service',
      desc: '',
      args: [],
    );
  }

  /// `Trip`
  String get text_trip {
    return Intl.message(
      'Trip',
      name: 'text_trip',
      desc: '',
      args: [],
    );
  }

  /// `No reviews yet`
  String get text_no_review {
    return Intl.message(
      'No reviews yet',
      name: 'text_no_review',
      desc: '',
      args: [],
    );
  }

  /// `Saved`
  String get text_saved {
    return Intl.message(
      'Saved',
      name: 'text_saved',
      desc: '',
      args: [],
    );
  }

  /// `See`
  String get text_see {
    return Intl.message(
      'See',
      name: 'text_see',
      desc: '',
      args: [],
    );
  }

  /// `Notification`
  String get text_notification {
    return Intl.message(
      'Notification',
      name: 'text_notification',
      desc: '',
      args: [],
    );
  }

  /// `Mark all notifications as read`
  String get text_mark_notifications {
    return Intl.message(
      'Mark all notifications as read',
      name: 'text_mark_notifications',
      desc: '',
      args: [],
    );
  }

  /// `Later`
  String get text_later {
    return Intl.message(
      'Later',
      name: 'text_later',
      desc: '',
      args: [],
    );
  }

  /// `Yes`
  String get text_yes {
    return Intl.message(
      'Yes',
      name: 'text_yes',
      desc: '',
      args: [],
    );
  }

  /// `Detailed notification`
  String get text_detailed_notification {
    return Intl.message(
      'Detailed notification',
      name: 'text_detailed_notification',
      desc: '',
      args: [],
    );
  }

  /// `Cancle`
  String get text_cancle {
    return Intl.message(
      'Cancle',
      name: 'text_cancle',
      desc: '',
      args: [],
    );
  }

  /// `Refund`
  String get text_refund {
    return Intl.message(
      'Refund',
      name: 'text_refund',
      desc: '',
      args: [],
    );
  }

  /// `Refund reason`
  String get text_refund_reason {
    return Intl.message(
      'Refund reason',
      name: 'text_refund_reason',
      desc: '',
      args: [],
    );
  }

  /// `Select refund reason`
  String get text_select_refund_reason {
    return Intl.message(
      'Select refund reason',
      name: 'text_select_refund_reason',
      desc: '',
      args: [],
    );
  }

  /// `Refund quantity`
  String get text_refund_quantity {
    return Intl.message(
      'Refund quantity',
      name: 'text_refund_quantity',
      desc: '',
      args: [],
    );
  }

  /// `Refund unit:`
  String get text_refund_unit {
    return Intl.message(
      'Refund unit:',
      name: 'text_refund_unit',
      desc: '',
      args: [],
    );
  }

  /// `Issue with order/ voucher`
  String get text_issue_with_order_or_voucher {
    return Intl.message(
      'Issue with order/ voucher',
      name: 'text_issue_with_order_or_voucher',
      desc: '',
      args: [],
    );
  }

  /// `Outside the scope of control`
  String get text_outside_the_scope_of_control {
    return Intl.message(
      'Outside the scope of control',
      name: 'text_outside_the_scope_of_control',
      desc: '',
      args: [],
    );
  }

  /// `Bad weather or natural disaster`
  String get text_bad_weather_or_natural_disaster {
    return Intl.message(
      'Bad weather or natural disaster',
      name: 'text_bad_weather_or_natural_disaster',
      desc: '',
      args: [],
    );
  }

  /// `Transportation delays or accidents`
  String get text_transportation_delays_or_accidents {
    return Intl.message(
      'Transportation delays or accidents',
      name: 'text_transportation_delays_or_accidents',
      desc: '',
      args: [],
    );
  }

  /// `Illness or outbreak of disease`
  String get text_illness_or_bursting_bubble {
    return Intl.message(
      'Illness or outbreak of disease',
      name: 'text_illness_or_bursting_bubble',
      desc: '',
      args: [],
    );
  }

  /// `Protests or social unrest`
  String get text_social_or_public_disturbances {
    return Intl.message(
      'Protests or social unrest',
      name: 'text_social_or_public_disturbances',
      desc: '',
      args: [],
    );
  }

  /// `Other conditions beyond control`
  String get text_other_external_conditions {
    return Intl.message(
      'Other conditions beyond control',
      name: 'text_other_external_conditions',
      desc: '',
      args: [],
    );
  }

  /// `Need to correct order`
  String get text_need_to_correct_order {
    return Intl.message(
      'Need to correct order',
      name: 'text_need_to_correct_order',
      desc: '',
      args: [],
    );
  }

  /// `Need to correct date/time/information of the customer/other information`
  String get text_need_to_correct_information {
    return Intl.message(
      'Need to correct date/time/information of the customer/other information',
      name: 'text_need_to_correct_information',
      desc: '',
      args: [],
    );
  }

  /// `Need to correct package`
  String get text_need_to_correct_package {
    return Intl.message(
      'Need to correct package',
      name: 'text_need_to_correct_package',
      desc: '',
      args: [],
    );
  }

  /// `Issue with using the promo code`
  String get text_use_promo_code_issue {
    return Intl.message(
      'Issue with using the promo code',
      name: 'text_use_promo_code_issue',
      desc: '',
      args: [],
    );
  }

  /// `Bad experience with service/product`
  String get text_bad_service_or_product_experience {
    return Intl.message(
      'Bad experience with service/product',
      name: 'text_bad_service_or_product_experience',
      desc: '',
      args: [],
    );
  }

  /// `Dissatisfied with the experience`
  String get text_dissatisfaction_with_experience {
    return Intl.message(
      'Dissatisfied with the experience',
      name: 'text_dissatisfaction_with_experience',
      desc: '',
      args: [],
    );
  }

  /// `Injury or serious incident`
  String get text_injury_or_severe_incident {
    return Intl.message(
      'Injury or serious incident',
      name: 'text_injury_or_severe_incident',
      desc: '',
      args: [],
    );
  }

  /// `Change of plan`
  String get text_change_of_plan {
    return Intl.message(
      'Change of plan',
      name: 'text_change_of_plan',
      desc: '',
      args: [],
    );
  }

  /// `Other reasons`
  String get text_other_reasons {
    return Intl.message(
      'Other reasons',
      name: 'text_other_reasons',
      desc: '',
      args: [],
    );
  }

  /// `State your reason.`
  String get text_state_your_reason {
    return Intl.message(
      'State your reason.',
      name: 'text_state_your_reason',
      desc: '',
      args: [],
    );
  }

  /// `Thông tin hoàn tiền`
  String get text_refund_information {
    return Intl.message(
      'Thông tin hoàn tiền',
      name: 'text_refund_information',
      desc: '',
      args: [],
    );
  }

  /// `Total refunded amount`
  String get text_total_refunded_amount {
    return Intl.message(
      'Total refunded amount',
      name: 'text_total_refunded_amount',
      desc: '',
      args: [],
    );
  }

  /// `Actual refund amount`
  String get text_actual_refund_amount {
    return Intl.message(
      'Actual refund amount',
      name: 'text_actual_refund_amount',
      desc: '',
      args: [],
    );
  }

  /// `Please note that there will be no refunds for additional fees and promotional codes you have used.`
  String get text_refund_note {
    return Intl.message(
      'Please note that there will be no refunds for additional fees and promotional codes you have used.',
      name: 'text_refund_note',
      desc: '',
      args: [],
    );
  }

  /// `Canceled`
  String get text_canceled {
    return Intl.message(
      'Canceled',
      name: 'text_canceled',
      desc: '',
      args: [],
    );
  }

  /// `Reset`
  String get text_reset_2 {
    return Intl.message(
      'Reset',
      name: 'text_reset_2',
      desc: '',
      args: [],
    );
  }

  /// `Refund Information`
  String get text_refund_information_2 {
    return Intl.message(
      'Refund Information',
      name: 'text_refund_information_2',
      desc: '',
      args: [],
    );
  }

  /// `Refund successful`
  String get text_refund_successful {
    return Intl.message(
      'Refund successful',
      name: 'text_refund_successful',
      desc: '',
      args: [],
    );
  }

  /// `Waiting for refund`
  String get text_waiting_for_refund {
    return Intl.message(
      'Waiting for refund',
      name: 'text_waiting_for_refund',
      desc: '',
      args: [],
    );
  }

  /// `We have processed the refund to your account. The time for the refund to your account depends on the payment method used to place the order.`
  String get text_refund_in_process {
    return Intl.message(
      'We have processed the refund to your account. The time for the refund to your account depends on the payment method used to place the order.',
      name: 'text_refund_in_process',
      desc: '',
      args: [],
    );
  }

  /// `We have checked the refund request to your account. The service provider will give you results within 7-14 days at the earliest.`
  String get text_refund_checking {
    return Intl.message(
      'We have checked the refund request to your account. The service provider will give you results within 7-14 days at the earliest.',
      name: 'text_refund_checking',
      desc: '',
      args: [],
    );
  }

  /// `Actual refund amount`
  String get text_actual_refund_amount_2 {
    return Intl.message(
      'Actual refund amount',
      name: 'text_actual_refund_amount_2',
      desc: '',
      args: [],
    );
  }

  /// `Amount paid`
  String get text_amount_paid {
    return Intl.message(
      'Amount paid',
      name: 'text_amount_paid',
      desc: '',
      args: [],
    );
  }

  /// `Request time:`
  String get text_request_time {
    return Intl.message(
      'Request time:',
      name: 'text_request_time',
      desc: '',
      args: [],
    );
  }

  /// `Refund time:`
  String get text_refund_time {
    return Intl.message(
      'Refund time:',
      name: 'text_refund_time',
      desc: '',
      args: [],
    );
  }

  /// `Cancel request has been sent`
  String get text_cancel_request_sent {
    return Intl.message(
      'Cancel request has been sent',
      name: 'text_cancel_request_sent',
      desc: '',
      args: [],
    );
  }

  /// `We have received your cancel request. The service provider will give you results within 7-14 days at the earliest.`
  String get text_cancel_request_received {
    return Intl.message(
      'We have received your cancel request. The service provider will give you results within 7-14 days at the earliest.',
      name: 'text_cancel_request_received',
      desc: '',
      args: [],
    );
  }

  /// `Understood`
  String get text_understood {
    return Intl.message(
      'Understood',
      name: 'text_understood',
      desc: '',
      args: [],
    );
  }

  /// `VNPay e-wallet (TCent refund up to 10% of value)`
  String get text_payment_vn_pay_method {
    return Intl.message(
      'VNPay e-wallet (TCent refund up to 10% of value)',
      name: 'text_payment_vn_pay_method',
      desc: '',
      args: [],
    );
  }

  /// `Payment failed!`
  String get text_payment_fail {
    return Intl.message(
      'Payment failed!',
      name: 'text_payment_fail',
      desc: '',
      args: [],
    );
  }

  /// `Please try again!`
  String get text_please_try_again {
    return Intl.message(
      'Please try again!',
      name: 'text_please_try_again',
      desc: '',
      args: [],
    );
  }

  /// `Try again`
  String get text_try_again {
    return Intl.message(
      'Try again',
      name: 'text_try_again',
      desc: '',
      args: [],
    );
  }

  /// `Invalid referral code! Please re-enter.`
  String get text_invalid_referral_code {
    return Intl.message(
      'Invalid referral code! Please re-enter.',
      name: 'text_invalid_referral_code',
      desc: '',
      args: [],
    );
  }

  /// `Pay later`
  String get text_payment_later {
    return Intl.message(
      'Pay later',
      name: 'text_payment_later',
      desc: '',
      args: [],
    );
  }

  /// `Shuttle Information (If applicable)`
  String get text_shuttle_information {
    return Intl.message(
      'Shuttle Information (If applicable)',
      name: 'text_shuttle_information',
      desc: '',
      args: [],
    );
  }

  /// `Hotel/Address`
  String get text_hotel_address {
    return Intl.message(
      'Hotel/Address',
      name: 'text_hotel_address',
      desc: '',
      args: [],
    );
  }

  /// `Fill in hotel name and address`
  String get text_fill_in_hotel_address {
    return Intl.message(
      'Fill in hotel name and address',
      name: 'text_fill_in_hotel_address',
      desc: '',
      args: [],
    );
  }

  /// `Pick up time at hotel`
  String get text_pick_up_time_at_hotel {
    return Intl.message(
      'Pick up time at hotel',
      name: 'text_pick_up_time_at_hotel',
      desc: '',
      args: [],
    );
  }

  /// `Example: 10:00`
  String get text_pick_up_time_at_hotel_hint {
    return Intl.message(
      'Example: 10:00',
      name: 'text_pick_up_time_at_hotel_hint',
      desc: '',
      args: [],
    );
  }

  /// `Contact method and details`
  String get text_contact_method {
    return Intl.message(
      'Contact method and details',
      name: 'text_contact_method',
      desc: '',
      args: [],
    );
  }

  /// `Example: Zalo +0987656484`
  String get text_contact_method_hint {
    return Intl.message(
      'Example: Zalo +0987656484',
      name: 'text_contact_method_hint',
      desc: '',
      args: [],
    );
  }

  /// `Proposed itinerary`
  String get text_proposed_itinerary {
    return Intl.message(
      'Proposed itinerary',
      name: 'text_proposed_itinerary',
      desc: '',
      args: [],
    );
  }

  /// `Fill in proposed itinerary`
  String get text_proposed_itinerary_hint {
    return Intl.message(
      'Fill in proposed itinerary',
      name: 'text_proposed_itinerary_hint',
      desc: '',
      args: [],
    );
  }

  /// `You have not set a Passcode for TripC ID!`
  String get text_you_have_not_set_a_passcode {
    return Intl.message(
      'You have not set a Passcode for TripC ID!',
      name: 'text_you_have_not_set_a_passcode',
      desc: '',
      args: [],
    );
  }

  /// `Without setting up a Passcode, you will not be able to use TripC ID to book tours, receive promotions and manage your account.`
  String get text_you_have_not_set_a_passcode_message {
    return Intl.message(
      'Without setting up a Passcode, you will not be able to use TripC ID to book tours, receive promotions and manage your account.',
      name: 'text_you_have_not_set_a_passcode_message',
      desc: '',
      args: [],
    );
  }

  /// `Review`
  String get text_review {
    return Intl.message(
      'Review',
      name: 'text_review',
      desc: '',
      args: [],
    );
  }

  /// `Reviews with images`
  String get text_reviews_with_images {
    return Intl.message(
      'Reviews with images',
      name: 'text_reviews_with_images',
      desc: '',
      args: [],
    );
  }

  /// `Neutral and negative reviews`
  String get text_neutral_and_negative_reviews {
    return Intl.message(
      'Neutral and negative reviews',
      name: 'text_neutral_and_negative_reviews',
      desc: '',
      args: [],
    );
  }

  /// `Post at`
  String get text_post_at {
    return Intl.message(
      'Post at',
      name: 'text_post_at',
      desc: '',
      args: [],
    );
  }

  /// `Supplier response`
  String get text_supplier_response {
    return Intl.message(
      'Supplier response',
      name: 'text_supplier_response',
      desc: '',
      args: [],
    );
  }

  /// `You have scrolled to the end.`
  String get text_you_have_scrolled_to_the_end {
    return Intl.message(
      'You have scrolled to the end.',
      name: 'text_you_have_scrolled_to_the_end',
      desc: '',
      args: [],
    );
  }

  /// `View more`
  String get text_view_more {
    return Intl.message(
      'View more',
      name: 'text_view_more',
      desc: '',
      args: [],
    );
  }

  /// `Collapse`
  String get text_collapse {
    return Intl.message(
      'Collapse',
      name: 'text_collapse',
      desc: '',
      args: [],
    );
  }

  /// `Bad`
  String get text_bad {
    return Intl.message(
      'Bad',
      name: 'text_bad',
      desc: '',
      args: [],
    );
  }

  /// `Good`
  String get text_good {
    return Intl.message(
      'Good',
      name: 'text_good',
      desc: '',
      args: [],
    );
  }

  /// `Very good`
  String get text_very_good {
    return Intl.message(
      'Very good',
      name: 'text_very_good',
      desc: '',
      args: [],
    );
  }

  /// `Review`
  String get text_reviews {
    return Intl.message(
      'Review',
      name: 'text_reviews',
      desc: '',
      args: [],
    );
  }

  /// `Contact`
  String get text_contact {
    return Intl.message(
      'Contact',
      name: 'text_contact',
      desc: '',
      args: [],
    );
  }

  /// `Add New Contact`
  String get text_add_new_contact {
    return Intl.message(
      'Add New Contact',
      name: 'text_add_new_contact',
      desc: '',
      args: [],
    );
  }

  /// `Edit Contact`
  String get text_edit_contact {
    return Intl.message(
      'Edit Contact',
      name: 'text_edit_contact',
      desc: '',
      args: [],
    );
  }

  /// `Phone`
  String get text_phone {
    return Intl.message(
      'Phone',
      name: 'text_phone',
      desc: '',
      args: [],
    );
  }

  /// `Save`
  String get text_save {
    return Intl.message(
      'Save',
      name: 'text_save',
      desc: '',
      args: [],
    );
  }

  /// `Set as default for orders`
  String get text_set_default_for_order {
    return Intl.message(
      'Set as default for orders',
      name: 'text_set_default_for_order',
      desc: '',
      args: [],
    );
  }

  /// `Contact name`
  String get text_contact_name {
    return Intl.message(
      'Contact name',
      name: 'text_contact_name',
      desc: '',
      args: [],
    );
  }

  /// `Input phone number`
  String get text_input_phone_number {
    return Intl.message(
      'Input phone number',
      name: 'text_input_phone_number',
      desc: '',
      args: [],
    );
  }

  /// `Delele Contact`
  String get text_delete_contact {
    return Intl.message(
      'Delele Contact',
      name: 'text_delete_contact',
      desc: '',
      args: [],
    );
  }

  /// `Delete this contact?`
  String get text_delete_contact_question {
    return Intl.message(
      'Delete this contact?',
      name: 'text_delete_contact_question',
      desc: '',
      args: [],
    );
  }

  /// `After adding your phone number, you can use it to link with other services in TripC.`
  String get text_link_phone_description {
    return Intl.message(
      'After adding your phone number, you can use it to link with other services in TripC.',
      name: 'text_link_phone_description',
      desc: '',
      args: [],
    );
  }

  /// `Phone number added successfully`
  String get text_link_phone_successfully {
    return Intl.message(
      'Phone number added successfully',
      name: 'text_link_phone_successfully',
      desc: '',
      args: [],
    );
  }

  /// `This phone number already exists!`
  String get text_phone_already_issue {
    return Intl.message(
      'This phone number already exists!',
      name: 'text_phone_already_issue',
      desc: '',
      args: [],
    );
  }

  /// `Enter your city of residence`
  String get text_hint_input_city {
    return Intl.message(
      'Enter your city of residence',
      name: 'text_hint_input_city',
      desc: '',
      args: [],
    );
  }

  /// `Request electronic invoice`
  String get text_request_electronic_invoice {
    return Intl.message(
      'Request electronic invoice',
      name: 'text_request_electronic_invoice',
      desc: '',
      args: [],
    );
  }

  /// `Invoice type:`
  String get text_invoice_type {
    return Intl.message(
      'Invoice type:',
      name: 'text_invoice_type',
      desc: '',
      args: [],
    );
  }

  /// `Personal`
  String get text_personal {
    return Intl.message(
      'Personal',
      name: 'text_personal',
      desc: '',
      args: [],
    );
  }

  /// `Company`
  String get text_company {
    return Intl.message(
      'Company',
      name: 'text_company',
      desc: '',
      args: [],
    );
  }

  /// `Electronic invoices can only be issued once before payment according to the information below. Please check and enter the information correctly. After selecting "Pay", the invoice information cannot be changed!`
  String get text_electronic_invoice_note {
    return Intl.message(
      'Electronic invoices can only be issued once before payment according to the information below. Please check and enter the information correctly. After selecting "Pay", the invoice information cannot be changed!',
      name: 'text_electronic_invoice_note',
      desc: '',
      args: [],
    );
  }

  /// `Invoice will be sent within 7 working days (excluding Sat - Sun)`
  String get text_invoice_message {
    return Intl.message(
      'Invoice will be sent within 7 working days (excluding Sat - Sun)',
      name: 'text_invoice_message',
      desc: '',
      args: [],
    );
  }

  /// `Disclaimer`
  String get text_disclaimer {
    return Intl.message(
      'Disclaimer',
      name: 'text_disclaimer',
      desc: '',
      args: [],
    );
  }

  /// `This electronic invoice for this order will be issued by the supplier and will be calculated based on the original value (before applying the voucher).`
  String get text_disclaimer_note_1 {
    return Intl.message(
      'This electronic invoice for this order will be issued by the supplier and will be calculated based on the original value (before applying the voucher).',
      name: 'text_disclaimer_note_1',
      desc: '',
      args: [],
    );
  }

  /// `In case the buyer does not provide information or does not send a request for an invoice. The supplier will use the information on the order to issue an electronic invoice.`
  String get text_disclaimer_note_2 {
    return Intl.message(
      'In case the buyer does not provide information or does not send a request for an invoice. The supplier will use the information on the order to issue an electronic invoice.',
      name: 'text_disclaimer_note_2',
      desc: '',
      args: [],
    );
  }

  /// `The supplier will not be responsible for any problems with tax declaration for invoices of 20 million VND or more paid in cash or any other personal payment method in TripC.`
  String get text_disclaimer_note_3 {
    return Intl.message(
      'The supplier will not be responsible for any problems with tax declaration for invoices of 20 million VND or more paid in cash or any other personal payment method in TripC.',
      name: 'text_disclaimer_note_3',
      desc: '',
      args: [],
    );
  }

  /// `Note: If the tax code is incorrect, the issued invoice will not have the buyer's tax code to ensure the legality of the invoice.`
  String get text_disclaimer_note_4 {
    return Intl.message(
      'Note: If the tax code is incorrect, the issued invoice will not have the buyer\'s tax code to ensure the legality of the invoice.',
      name: 'text_disclaimer_note_4',
      desc: '',
      args: [],
    );
  }

  /// `Submit request`
  String get text_submit_request {
    return Intl.message(
      'Submit request',
      name: 'text_submit_request',
      desc: '',
      args: [],
    );
  }

  /// `Electronic invoice`
  String get text_electronic_invoice {
    return Intl.message(
      'Electronic invoice',
      name: 'text_electronic_invoice',
      desc: '',
      args: [],
    );
  }

  /// `Request Now`
  String get text_request_now {
    return Intl.message(
      'Request Now',
      name: 'text_request_now',
      desc: '',
      args: [],
    );
  }

  /// `Requested`
  String get text_requested {
    return Intl.message(
      'Requested',
      name: 'text_requested',
      desc: '',
      args: [],
    );
  }

  /// `Processing`
  String get text_processing {
    return Intl.message(
      'Processing',
      name: 'text_processing',
      desc: '',
      args: [],
    );
  }

  /// `Business name`
  String get text_business_name {
    return Intl.message(
      'Business name',
      name: 'text_business_name',
      desc: '',
      args: [],
    );
  }

  /// `Enter business name`
  String get text_enter_business_name {
    return Intl.message(
      'Enter business name',
      name: 'text_enter_business_name',
      desc: '',
      args: [],
    );
  }

  /// `Address:`
  String get text_address {
    return Intl.message(
      'Address:',
      name: 'text_address',
      desc: '',
      args: [],
    );
  }

  /// `Business address`
  String get text_business_address {
    return Intl.message(
      'Business address',
      name: 'text_business_address',
      desc: '',
      args: [],
    );
  }

  /// `Enter your address`
  String get text_enter_address {
    return Intl.message(
      'Enter your address',
      name: 'text_enter_address',
      desc: '',
      args: [],
    );
  }

  /// `Invoice email:`
  String get text_invoice_email {
    return Intl.message(
      'Invoice email:',
      name: 'text_invoice_email',
      desc: '',
      args: [],
    );
  }

  /// `Tax code`
  String get text_tax_code {
    return Intl.message(
      'Tax code',
      name: 'text_tax_code',
      desc: '',
      args: [],
    );
  }

  /// `Enter tax code`
  String get text_enter_tax_code {
    return Intl.message(
      'Enter tax code',
      name: 'text_enter_tax_code',
      desc: '',
      args: [],
    );
  }

  /// `Includes`
  String get text_includes {
    return Intl.message(
      'Includes',
      name: 'text_includes',
      desc: '',
      args: [],
    );
  }

  /// `How to Use`
  String get text_how_to_use {
    return Intl.message(
      'How to Use',
      name: 'text_how_to_use',
      desc: '',
      args: [],
    );
  }

  /// `Before Placing`
  String get text_before_placing {
    return Intl.message(
      'Before Placing',
      name: 'text_before_placing',
      desc: '',
      args: [],
    );
  }

  /// `Processed`
  String get text_processed {
    return Intl.message(
      'Processed',
      name: 'text_processed',
      desc: '',
      args: [],
    );
  }

  /// `Trip Review`
  String get text_trip_review {
    return Intl.message(
      'Trip Review',
      name: 'text_trip_review',
      desc: '',
      args: [],
    );
  }

  /// `Contact TripC`
  String get text_contact_tripc {
    return Intl.message(
      'Contact TripC',
      name: 'text_contact_tripc',
      desc: '',
      args: [],
    );
  }

  /// `*Contact TripC immediately if you want to change information.`
  String get text_contact_tripc_message {
    return Intl.message(
      '*Contact TripC immediately if you want to change information.',
      name: 'text_contact_tripc_message',
      desc: '',
      args: [],
    );
  }

  /// `People`
  String get text_people {
    return Intl.message(
      'People',
      name: 'text_people',
      desc: '',
      args: [],
    );
  }

  /// `Saved Tour`
  String get text_saved_tour {
    return Intl.message(
      'Saved Tour',
      name: 'text_saved_tour',
      desc: '',
      args: [],
    );
  }

  /// `Recently Viewed`
  String get text_recent_view {
    return Intl.message(
      'Recently Viewed',
      name: 'text_recent_view',
      desc: '',
      args: [],
    );
  }

  /// `Showing viewing history for the last 30 days`
  String get text_showing_view_history_in_30_days {
    return Intl.message(
      'Showing viewing history for the last 30 days',
      name: 'text_showing_view_history_in_30_days',
      desc: '',
      args: [],
    );
  }

  /// `review`
  String get text_rating_2 {
    return Intl.message(
      'review',
      name: 'text_rating_2',
      desc: '',
      args: [],
    );
  }

  /// `Today`
  String get text_today {
    return Intl.message(
      'Today',
      name: 'text_today',
      desc: '',
      args: [],
    );
  }

  /// `Yesterday`
  String get text_yesterday {
    return Intl.message(
      'Yesterday',
      name: 'text_yesterday',
      desc: '',
      args: [],
    );
  }

  /// `{content} previous days`
  String text_previous_day(int content) {
    return Intl.message(
      '$content previous days',
      name: 'text_previous_day',
      desc: '',
      args: [content],
    );
  }

  /// `Delete Browsing History`
  String get text_delete_browsing_history {
    return Intl.message(
      'Delete Browsing History',
      name: 'text_delete_browsing_history',
      desc: '',
      args: [],
    );
  }

  /// `After deleting, you will no longer be able to view your browsing history. Do you still want to delete?`
  String get text_delete_browsing_history_note {
    return Intl.message(
      'After deleting, you will no longer be able to view your browsing history. Do you still want to delete?',
      name: 'text_delete_browsing_history_note',
      desc: '',
      args: [],
    );
  }

  /// `No browsing history.\nPlease return to the home page and start exploring TripC AI's services and features!`
  String get text_no_broswing_history {
    return Intl.message(
      'No browsing history.\nPlease return to the home page and start exploring TripC AI\'s services and features!',
      name: 'text_no_broswing_history',
      desc: '',
      args: [],
    );
  }

  /// `Please check your network settings or try again.`
  String get text_no_internet {
    return Intl.message(
      'Please check your network settings or try again.',
      name: 'text_no_internet',
      desc: '',
      args: [],
    );
  }

  /// `Adult Information`
  String get text_adult_info {
    return Intl.message(
      'Adult Information',
      name: 'text_adult_info',
      desc: '',
      args: [],
    );
  }

  /// `Child Information`
  String get text_child_info {
    return Intl.message(
      'Child Information',
      name: 'text_child_info',
      desc: '',
      args: [],
    );
  }

  /// `Introduction to TripC AI`
  String get text_tripc_about {
    return Intl.message(
      'Introduction to TripC AI',
      name: 'text_tripc_about',
      desc: '',
      args: [],
    );
  }

  /// `Terms & Conditions`
  String get text_tripc_term_and_policy {
    return Intl.message(
      'Terms & Conditions',
      name: 'text_tripc_term_and_policy',
      desc: '',
      args: [],
    );
  }

  /// `Bronze Tier`
  String get text_bronze_tier {
    return Intl.message(
      'Bronze Tier',
      name: 'text_bronze_tier',
      desc: '',
      args: [],
    );
  }

  /// `Platinum Tier`
  String get text_platinum_tier {
    return Intl.message(
      'Platinum Tier',
      name: 'text_platinum_tier',
      desc: '',
      args: [],
    );
  }

  /// `Open Settings`
  String get text_open_setting {
    return Intl.message(
      'Open Settings',
      name: 'text_open_setting',
      desc: '',
      args: [],
    );
  }

  /// `Permission Required`
  String get text_permission_required {
    return Intl.message(
      'Permission Required',
      name: 'text_permission_required',
      desc: '',
      args: [],
    );
  }

  /// `Please enable permission in settings to continue.`
  String get text_permission_note {
    return Intl.message(
      'Please enable permission in settings to continue.',
      name: 'text_permission_note',
      desc: '',
      args: [],
    );
  }

  /// `Upload Image`
  String get text_upload_image_title {
    return Intl.message(
      'Upload Image',
      name: 'text_upload_image_title',
      desc: '',
      args: [],
    );
  }

  /// `Choose an image source`
  String get text_choose_image_source {
    return Intl.message(
      'Choose an image source',
      name: 'text_choose_image_source',
      desc: '',
      args: [],
    );
  }

  /// `Photos`
  String get text_photo {
    return Intl.message(
      'Photos',
      name: 'text_photo',
      desc: '',
      args: [],
    );
  }

  /// `Camera`
  String get text_camera {
    return Intl.message(
      'Camera',
      name: 'text_camera',
      desc: '',
      args: [],
    );
  }

  /// `Bronze`
  String get text_bronze_rank {
    return Intl.message(
      'Bronze',
      name: 'text_bronze_rank',
      desc: '',
      args: [],
    );
  }

  /// `Silver`
  String get text_silver_rank {
    return Intl.message(
      'Silver',
      name: 'text_silver_rank',
      desc: '',
      args: [],
    );
  }

  /// `Gold`
  String get text_gold_rank {
    return Intl.message(
      'Gold',
      name: 'text_gold_rank',
      desc: '',
      args: [],
    );
  }

  /// `Platinum`
  String get text_platinum_rank {
    return Intl.message(
      'Platinum',
      name: 'text_platinum_rank',
      desc: '',
      args: [],
    );
  }

  /// `Diamond`
  String get text_diamond_rank {
    return Intl.message(
      'Diamond',
      name: 'text_diamond_rank',
      desc: '',
      args: [],
    );
  }

  /// `People are searching for`
  String get text_people_are_searching {
    return Intl.message(
      'People are searching for',
      name: 'text_people_are_searching',
      desc: '',
      args: [],
    );
  }

  /// `Top search`
  String get text_top_search {
    return Intl.message(
      'Top search',
      name: 'text_top_search',
      desc: '',
      args: [],
    );
  }

  /// `Popular tours`
  String get text_popular_tour {
    return Intl.message(
      'Popular tours',
      name: 'text_popular_tour',
      desc: '',
      args: [],
    );
  }

  /// `No results found.\nPlease try again.`
  String get text_search_not_found {
    return Intl.message(
      'No results found.\nPlease try again.',
      name: 'text_search_not_found',
      desc: '',
      args: [],
    );
  }

  /// `No results found.`
  String get text_not_found {
    return Intl.message(
      'No results found.',
      name: 'text_not_found',
      desc: '',
      args: [],
    );
  }

  /// `Clear all recent search results?`
  String get text_delete_recent_search {
    return Intl.message(
      'Clear all recent search results?',
      name: 'text_delete_recent_search',
      desc: '',
      args: [],
    );
  }

  /// `The new email address must be different from the current email address!`
  String get text_email_must_be_different {
    return Intl.message(
      'The new email address must be different from the current email address!',
      name: 'text_email_must_be_different',
      desc: '',
      args: [],
    );
  }

  /// `*The verification code is valid for 5 minutes after you receive it.`
  String get text_valid_verification_code_for_5_mins {
    return Intl.message(
      '*The verification code is valid for 5 minutes after you receive it.',
      name: 'text_valid_verification_code_for_5_mins',
      desc: '',
      args: [],
    );
  }

  /// `Link email successfully`
  String get text_link_email_successfully {
    return Intl.message(
      'Link email successfully',
      name: 'text_link_email_successfully',
      desc: '',
      args: [],
    );
  }

  /// `You have successfully linked your email.\nUse the email you just set up to log in next time.`
  String get text_link_email_successful_content {
    return Intl.message(
      'You have successfully linked your email.\nUse the email you just set up to log in next time.',
      name: 'text_link_email_successful_content',
      desc: '',
      args: [],
    );
  }

  /// `Agree`
  String get text_agree {
    return Intl.message(
      'Agree',
      name: 'text_agree',
      desc: '',
      args: [],
    );
  }

  /// `Ticket Information`
  String get text_ticket_info {
    return Intl.message(
      'Ticket Information',
      name: 'text_ticket_info',
      desc: '',
      args: [],
    );
  }

  /// `Your QR Code`
  String get text_your_qr {
    return Intl.message(
      'Your QR Code',
      name: 'text_your_qr',
      desc: '',
      args: [],
    );
  }

  /// `Ticket ID No `
  String get text_ticket_id_no {
    return Intl.message(
      'Ticket ID No ',
      name: 'text_ticket_id_no',
      desc: '',
      args: [],
    );
  }

  /// `Passenger Name`
  String get text_passenger_name {
    return Intl.message(
      'Passenger Name',
      name: 'text_passenger_name',
      desc: '',
      args: [],
    );
  }

  /// `Children's Full Name:`
  String get text_child_name {
    return Intl.message(
      'Children\'s Full Name:',
      name: 'text_child_name',
      desc: '',
      args: [],
    );
  }

  /// `Guardian:`
  String get text_guardian_name {
    return Intl.message(
      'Guardian:',
      name: 'text_guardian_name',
      desc: '',
      args: [],
    );
  }

  /// `Guardian Phone Number:`
  String get text_guardian_phone_number {
    return Intl.message(
      'Guardian Phone Number:',
      name: 'text_guardian_phone_number',
      desc: '',
      args: [],
    );
  }

  /// `Enter child's full name`
  String get text_child_name_hint {
    return Intl.message(
      'Enter child\'s full name',
      name: 'text_child_name_hint',
      desc: '',
      args: [],
    );
  }

  /// `Enter guardian's full name`
  String get text_guardian_name_hint {
    return Intl.message(
      'Enter guardian\'s full name',
      name: 'text_guardian_name_hint',
      desc: '',
      args: [],
    );
  }

  /// `Enter guardian's phone number`
  String get text_guardian_phone_number_hint {
    return Intl.message(
      'Enter guardian\'s phone number',
      name: 'text_guardian_phone_number_hint',
      desc: '',
      args: [],
    );
  }

  /// `Waiting for receipt`
  String get text_waiting_receive {
    return Intl.message(
      'Waiting for receipt',
      name: 'text_waiting_receive',
      desc: '',
      args: [],
    );
  }

  /// `Waiting for completion`
  String get text_waiting_for_completion {
    return Intl.message(
      'Waiting for completion',
      name: 'text_waiting_for_completion',
      desc: '',
      args: [],
    );
  }

  /// `For any questions or support requests, please contact TripC via email `
  String get text_contact_message_1 {
    return Intl.message(
      'For any questions or support requests, please contact TripC via email ',
      name: 'text_contact_message_1',
      desc: '',
      args: [],
    );
  }

  /// `<EMAIL> `
  String get text_contact_tripc_email {
    return Intl.message(
      '<EMAIL> ',
      name: 'text_contact_tripc_email',
      desc: '',
      args: [],
    );
  }

  /// `for quick advice!`
  String get text_contact_message_2 {
    return Intl.message(
      'for quick advice!',
      name: 'text_contact_message_2',
      desc: '',
      args: [],
    );
  }

  /// `This email is already used. Please login!`
  String get text_email_already_issue {
    return Intl.message(
      'This email is already used. Please login!',
      name: 'text_email_already_issue',
      desc: '',
      args: [],
    );
  }

  /// `Adult ticket {content} (Over 10 years old)`
  String text_adult_ticket(String content) {
    return Intl.message(
      'Adult ticket $content (Over 10 years old)',
      name: 'text_adult_ticket',
      desc: '',
      args: [content],
    );
  }

  /// `Child ticket {content} (5 - 9 years old)`
  String text_child_ticket(String content) {
    return Intl.message(
      'Child ticket $content (5 - 9 years old)',
      name: 'text_child_ticket',
      desc: '',
      args: [content],
    );
  }

  /// `Passenger`
  String get text_passenger_title {
    return Intl.message(
      'Passenger',
      name: 'text_passenger_title',
      desc: '',
      args: [],
    );
  }

  /// `Add Phone Number`
  String get text_add_phone_number {
    return Intl.message(
      'Add Phone Number',
      name: 'text_add_phone_number',
      desc: '',
      args: [],
    );
  }

  /// `Password cannot contain spaces!`
  String get text_password_without_space {
    return Intl.message(
      'Password cannot contain spaces!',
      name: 'text_password_without_space',
      desc: '',
      args: [],
    );
  }

  /// `Password must be at least 8 characters!`
  String get text_password_must_be_at_last_8_characters {
    return Intl.message(
      'Password must be at least 8 characters!',
      name: 'text_password_must_be_at_last_8_characters',
      desc: '',
      args: [],
    );
  }

  /// `Password must contain at least one uppercase letter!`
  String get text_password_uppercase_valid {
    return Intl.message(
      'Password must contain at least one uppercase letter!',
      name: 'text_password_uppercase_valid',
      desc: '',
      args: [],
    );
  }

  /// `Password must contain at least one lowercase letter!`
  String get text_password_lowercase_valid {
    return Intl.message(
      'Password must contain at least one lowercase letter!',
      name: 'text_password_lowercase_valid',
      desc: '',
      args: [],
    );
  }

  /// `Password must contain at least one digit!`
  String get text_password_one_number_valid {
    return Intl.message(
      'Password must contain at least one digit!',
      name: 'text_password_one_number_valid',
      desc: '',
      args: [],
    );
  }

  /// `Password must contain at least one special character!`
  String get text_password_must_be_1_special {
    return Intl.message(
      'Password must contain at least one special character!',
      name: 'text_password_must_be_1_special',
      desc: '',
      args: [],
    );
  }

  /// `Your OTP code has expired`
  String get text_otp_expired {
    return Intl.message(
      'Your OTP code has expired',
      name: 'text_otp_expired',
      desc: '',
      args: [],
    );
  }

  /// `Please enter the correct OTP code`
  String get text_otp_incorrect {
    return Intl.message(
      'Please enter the correct OTP code',
      name: 'text_otp_incorrect',
      desc: '',
      args: [],
    );
  }

  /// `Send request for electronic invoice successfully`
  String get text_invoice_success {
    return Intl.message(
      'Send request for electronic invoice successfully',
      name: 'text_invoice_success',
      desc: '',
      args: [],
    );
  }

  /// `We are processing your request.\nAn invoice will be sent to your email after you complete your order.`
  String get text_invoice_message_popup {
    return Intl.message(
      'We are processing your request.\nAn invoice will be sent to your email after you complete your order.',
      name: 'text_invoice_message_popup',
      desc: '',
      args: [],
    );
  }

  /// `Copied!`
  String get text_copied {
    return Intl.message(
      'Copied!',
      name: 'text_copied',
      desc: '',
      args: [],
    );
  }

  /// `There are currently no promotional codes available.\nIf you have a promotional code, you can and apply it`
  String get text_promotional_empty_message {
    return Intl.message(
      'There are currently no promotional codes available.\nIf you have a promotional code, you can and apply it',
      name: 'text_promotional_empty_message',
      desc: '',
      args: [],
    );
  }

  /// `No trips yet.\nBook tickets to experience the trip with TripC!`
  String get text_my_tripc_empty_message {
    return Intl.message(
      'No trips yet.\nBook tickets to experience the trip with TripC!',
      name: 'text_my_tripc_empty_message',
      desc: '',
      args: [],
    );
  }

  /// `Privacy Policy at TripC`
  String get text_privacy_policy_at_tripc {
    return Intl.message(
      'Privacy Policy at TripC',
      name: 'text_privacy_policy_at_tripc',
      desc: '',
      args: [],
    );
  }

  /// `TripC is committed to protecting your personal data and providing privacy control options so that you can use the application safely and transparently.`
  String get text_commitment_to_data_protection {
    return Intl.message(
      'TripC is committed to protecting your personal data and providing privacy control options so that you can use the application safely and transparently.',
      name: 'text_commitment_to_data_protection',
      desc: '',
      args: [],
    );
  }

  /// `1. Personal Data Management`
  String get text_personal_data_management {
    return Intl.message(
      '1. Personal Data Management',
      name: 'text_personal_data_management',
      desc: '',
      args: [],
    );
  }

  /// `Edit personal information: Update full name, phone number, email, address, payment information.`
  String get text_edit_personal_information {
    return Intl.message(
      'Edit personal information: Update full name, phone number, email, address, payment information.',
      name: 'text_edit_personal_information',
      desc: '',
      args: [],
    );
  }

  /// `Delete account & data: If you want to stop using TripC, you can request to permanently delete the account and all related data.`
  String get text_delete_account_and_data {
    return Intl.message(
      'Delete account & data: If you want to stop using TripC, you can request to permanently delete the account and all related data.',
      name: 'text_delete_account_and_data',
      desc: '',
      args: [],
    );
  }

  /// `Download personal data: You can request to download all transaction history, booked tours, and stored personal information.`
  String get text_download_personal_data {
    return Intl.message(
      'Download personal data: You can request to download all transaction history, booked tours, and stored personal information.',
      name: 'text_download_personal_data',
      desc: '',
      args: [],
    );
  }

  /// `2. Security Settings`
  String get text_security_settings {
    return Intl.message(
      '2. Security Settings',
      name: 'text_security_settings',
      desc: '',
      args: [],
    );
  }

  /// `If you remain inactive for a specified period (5, 10, 15 minutes...), the system will automatically log you out to protect your account.`
  String get text_inactive_timeout_logout {
    return Intl.message(
      'If you remain inactive for a specified period (5, 10, 15 minutes...), the system will automatically log you out to protect your account.',
      name: 'text_inactive_timeout_logout',
      desc: '',
      args: [],
    );
  }

  /// `3. Data Sharing Rights`
  String get text_data_sharing_rights {
    return Intl.message(
      '3. Data Sharing Rights',
      name: 'text_data_sharing_rights',
      desc: '',
      args: [],
    );
  }

  /// `Enable/disable receiving promotional emails and notifications about relevant tours.`
  String get text_enable_disable_promotion_emails {
    return Intl.message(
      'Enable/disable receiving promotional emails and notifications about relevant tours.',
      name: 'text_enable_disable_promotion_emails',
      desc: '',
      args: [],
    );
  }

  /// `Used to upload profile images and rate tours.`
  String get text_upload_photos_and_reviews {
    return Intl.message(
      'Used to upload profile images and rate tours.',
      name: 'text_upload_photos_and_reviews',
      desc: '',
      args: [],
    );
  }

  /// `You can change this right in the Device Settings.`
  String get text_change_data_sharing_rights {
    return Intl.message(
      'You can change this right in the Device Settings.',
      name: 'text_change_data_sharing_rights',
      desc: '',
      args: [],
    );
  }

  /// `4. Activity & Security Log`
  String get text_security_activity_and_log {
    return Intl.message(
      '4. Activity & Security Log',
      name: 'text_security_activity_and_log',
      desc: '',
      args: [],
    );
  }

  /// `Login history`
  String get text_login_history {
    return Intl.message(
      'Login history',
      name: 'text_login_history',
      desc: '',
      args: [],
    );
  }

  /// `View recent login history, including time & device.`
  String get text_view_recent_login {
    return Intl.message(
      'View recent login history, including time & device.',
      name: 'text_view_recent_login',
      desc: '',
      args: [],
    );
  }

  /// `Receive alerts for unusual login attempts.`
  String get text_receive_login_alerts {
    return Intl.message(
      'Receive alerts for unusual login attempts.',
      name: 'text_receive_login_alerts',
      desc: '',
      args: [],
    );
  }

  /// `History of privacy changes`
  String get text_privacy_changes_history {
    return Intl.message(
      'History of privacy changes',
      name: 'text_privacy_changes_history',
      desc: '',
      args: [],
    );
  }

  /// `Review changes to privacy settings that you have made.`
  String get text_view_privacy_changes {
    return Intl.message(
      'Review changes to privacy settings that you have made.',
      name: 'text_view_privacy_changes',
      desc: '',
      args: [],
    );
  }

  /// `Application activity`
  String get text_application_activity {
    return Intl.message(
      'Application activity',
      name: 'text_application_activity',
      desc: '',
      args: [],
    );
  }

  /// `View the history of tour bookings, payments, and cancellations.`
  String get text_view_booking_payment_cancellation_history {
    return Intl.message(
      'View the history of tour bookings, payments, and cancellations.',
      name: 'text_view_booking_payment_cancellation_history',
      desc: '',
      args: [],
    );
  }

  /// `If you have any questions, please contact TripC at `
  String get text_contact_tripc_at {
    return Intl.message(
      'If you have any questions, please contact TripC at ',
      name: 'text_contact_tripc_at',
      desc: '',
      args: [],
    );
  }

  /// ` for the quickest support!`
  String get text_to_be_supported_quick {
    return Intl.message(
      ' for the quickest support!',
      name: 'text_to_be_supported_quick',
      desc: '',
      args: [],
    );
  }

  /// ` or `
  String get text_or_2 {
    return Intl.message(
      ' or ',
      name: 'text_or_2',
      desc: '',
      args: [],
    );
  }

  /// `Terms & Conditions`
  String get text_term_and_condition_title {
    return Intl.message(
      'Terms & Conditions',
      name: 'text_term_and_condition_title',
      desc: '',
      args: [],
    );
  }

  /// `Terms and Conditions of Using TripC App`
  String get text_term_and_condition {
    return Intl.message(
      'Terms and Conditions of Using TripC App',
      name: 'text_term_and_condition',
      desc: '',
      args: [],
    );
  }

  /// `Welcome to TripC – the leading tour booking app! Before using the service, please read the Terms and Conditions below. Using the app means you agree to these terms.`
  String get text_welcome_message {
    return Intl.message(
      'Welcome to TripC – the leading tour booking app! Before using the service, please read the Terms and Conditions below. Using the app means you agree to these terms.',
      name: 'text_welcome_message',
      desc: '',
      args: [],
    );
  }

  /// `1. Registration & User Account`
  String get text_register_and_user_account {
    return Intl.message(
      '1. Registration & User Account',
      name: 'text_register_and_user_account',
      desc: '',
      args: [],
    );
  }

  /// `Users are required to provide accurate information when registering for an account.`
  String get text_user_information {
    return Intl.message(
      'Users are required to provide accurate information when registering for an account.',
      name: 'text_user_information',
      desc: '',
      args: [],
    );
  }

  /// `You are responsible for securing your login information and not sharing your account with others.`
  String get text_account_responsibility {
    return Intl.message(
      'You are responsible for securing your login information and not sharing your account with others.',
      name: 'text_account_responsibility',
      desc: '',
      args: [],
    );
  }

  /// `TripC reserves the right to temporarily lock or cancel accounts if fraudulent activity or violations of the terms are detected.`
  String get text_account_privacy {
    return Intl.message(
      'TripC reserves the right to temporarily lock or cancel accounts if fraudulent activity or violations of the terms are detected.',
      name: 'text_account_privacy',
      desc: '',
      args: [],
    );
  }

  /// `2. Booking & Payment`
  String get text_booking_and_payment {
    return Intl.message(
      '2. Booking & Payment',
      name: 'text_booking_and_payment',
      desc: '',
      args: [],
    );
  }

  /// `The tour price displayed in the app may vary depending on promotional programs and time conditions.`
  String get text_tour_price {
    return Intl.message(
      'The tour price displayed in the app may vary depending on promotional programs and time conditions.',
      name: 'text_tour_price',
      desc: '',
      args: [],
    );
  }

  /// `Payments are processed through various methods such as bank cards, e-wallets, or bank transfers.`
  String get text_payment_method_condition {
    return Intl.message(
      'Payments are processed through various methods such as bank cards, e-wallets, or bank transfers.',
      name: 'text_payment_method_condition',
      desc: '',
      args: [],
    );
  }

  /// `After successful payment, TripC will send booking confirmation via email or the app.`
  String get text_payment_confirmation {
    return Intl.message(
      'After successful payment, TripC will send booking confirmation via email or the app.',
      name: 'text_payment_confirmation',
      desc: '',
      args: [],
    );
  }

  /// `3. Tour Cancellation & Refund`
  String get text_tour_cancellation {
    return Intl.message(
      '3. Tour Cancellation & Refund',
      name: 'text_tour_cancellation',
      desc: '',
      args: [],
    );
  }

  /// `The tour cancellation and refund policy depend on each service provider.`
  String get text_cancellation_policy {
    return Intl.message(
      'The tour cancellation and refund policy depend on each service provider.',
      name: 'text_cancellation_policy',
      desc: '',
      args: [],
    );
  }

  /// `Some tours may not support refunds or only partially refund based on cancellation time.`
  String get text_non_refundable {
    return Intl.message(
      'Some tours may not support refunds or only partially refund based on cancellation time.',
      name: 'text_non_refundable',
      desc: '',
      args: [],
    );
  }

  /// `For cancellation requests, please contact TripC for support.`
  String get text_contact_support {
    return Intl.message(
      'For cancellation requests, please contact TripC for support.',
      name: 'text_contact_support',
      desc: '',
      args: [],
    );
  }

  /// `4. User Rights & Responsibilities`
  String get text_user_rights {
    return Intl.message(
      '4. User Rights & Responsibilities',
      name: 'text_user_rights',
      desc: '',
      args: [],
    );
  }

  /// `Comply with TripC's rules and the terms of service provided by partners. Do not use the app for illegal, fraudulent, or disruptive purposes.`
  String get text_unauthorized_usage {
    return Intl.message(
      'Comply with TripC\'s rules and the terms of service provided by partners. Do not use the app for illegal, fraudulent, or disruptive purposes.',
      name: 'text_unauthorized_usage',
      desc: '',
      args: [],
    );
  }

  /// `Do not copy, distribute, or use TripC's content without permission.`
  String get text_responsibility_notice {
    return Intl.message(
      'Do not copy, distribute, or use TripC\'s content without permission.',
      name: 'text_responsibility_notice',
      desc: '',
      args: [],
    );
  }

  /// `5. TripC's Rights & Responsibilities`
  String get text_tripc_responsibility {
    return Intl.message(
      '5. TripC\'s Rights & Responsibilities',
      name: 'text_tripc_responsibility',
      desc: '',
      args: [],
    );
  }

  /// `Ensure the provision of services as described in the app.`
  String get text_service_provision {
    return Intl.message(
      'Ensure the provision of services as described in the app.',
      name: 'text_service_provision',
      desc: '',
      args: [],
    );
  }

  /// `Provide support to customers during the booking process and resolve any issues that arise.`
  String get text_support_during_booking {
    return Intl.message(
      'Provide support to customers during the booking process and resolve any issues that arise.',
      name: 'text_support_during_booking',
      desc: '',
      args: [],
    );
  }

  /// `6. Privacy Policy`
  String get text_privacy_policy {
    return Intl.message(
      '6. Privacy Policy',
      name: 'text_privacy_policy',
      desc: '',
      args: [],
    );
  }

  /// `TripC is committed to protecting users' personal data according to the privacy policy.`
  String get text_data_protection {
    return Intl.message(
      'TripC is committed to protecting users\' personal data according to the privacy policy.',
      name: 'text_data_protection',
      desc: '',
      args: [],
    );
  }

  /// `We do not share your personal information with third parties without your consent.`
  String get text_data_sharing {
    return Intl.message(
      'We do not share your personal information with third parties without your consent.',
      name: 'text_data_sharing',
      desc: '',
      args: [],
    );
  }

  /// `7. Terms Modification`
  String get text_terms_change {
    return Intl.message(
      '7. Terms Modification',
      name: 'text_terms_change',
      desc: '',
      args: [],
    );
  }

  /// `TripC reserves the right to modify or update the terms without prior notice.`
  String get text_terms_modification {
    return Intl.message(
      'TripC reserves the right to modify or update the terms without prior notice.',
      name: 'text_terms_modification',
      desc: '',
      args: [],
    );
  }

  /// `Continuing to use the service after terms changes means you agree to the updated terms.`
  String get text_terms_update_notice {
    return Intl.message(
      'Continuing to use the service after terms changes means you agree to the updated terms.',
      name: 'text_terms_update_notice',
      desc: '',
      args: [],
    );
  }

  /// `TripC AI – The Application for Convenient Tour Booking & Attractive Discounts!`
  String get text_tripc_intro_title {
    return Intl.message(
      'TripC AI – The Application for Convenient Tour Booking & Attractive Discounts!',
      name: 'text_tripc_intro_title',
      desc: '',
      args: [],
    );
  }

  /// `TripC is a leading tour booking platform, providing you with an easy, quick, and cost-effective way to plan your trips. With thousands of tours within the country and internationally, TripC helps you explore beautiful destinations at great prices with quality services.`
  String get text_tripc_intro_description {
    return Intl.message(
      'TripC is a leading tour booking platform, providing you with an easy, quick, and cost-effective way to plan your trips. With thousands of tours within the country and internationally, TripC helps you explore beautiful destinations at great prices with quality services.',
      name: 'text_tripc_intro_description',
      desc: '',
      args: [],
    );
  }

  /// `Why choose TripC`
  String get text_why_choose_tripc {
    return Intl.message(
      'Why choose TripC',
      name: 'text_why_choose_tripc',
      desc: '',
      args: [],
    );
  }

  /// `✅ Fast and secure tour booking: With just a few clicks, you can find and book the tour that suits your needs.`
  String get text_choose_tripc_point_1 {
    return Intl.message(
      '✅ Fast and secure tour booking: With just a few clicks, you can find and book the tour that suits your needs.',
      name: 'text_choose_tripc_point_1',
      desc: '',
      args: [],
    );
  }

  /// `✅ Attractive discounts: Continuously updated promotional programs and discounts for customers.`
  String get text_choose_tripc_point_2 {
    return Intl.message(
      '✅ Attractive discounts: Continuously updated promotional programs and discounts for customers.',
      name: 'text_choose_tripc_point_2',
      desc: '',
      args: [],
    );
  }

  /// `✅ Quality service: Clear itinerary, ensuring safety for customers.`
  String get text_choose_tripc_point_3 {
    return Intl.message(
      '✅ Quality service: Clear itinerary, ensuring safety for customers.',
      name: 'text_choose_tripc_point_3',
      desc: '',
      args: [],
    );
  }

  /// `✅ 24/7 support: Our customer service team is always ready to assist and support you on your journey.`
  String get text_choose_tripc_point_4 {
    return Intl.message(
      '✅ 24/7 support: Our customer service team is always ready to assist and support you on your journey.',
      name: 'text_choose_tripc_point_4',
      desc: '',
      args: [],
    );
  }

  /// `No account found with this email. Please check again or try another email`
  String get text_email_not_found {
    return Intl.message(
      'No account found with this email. Please check again or try another email',
      name: 'text_email_not_found',
      desc: '',
      args: [],
    );
  }

  /// `Home`
  String get text_home {
    return Intl.message(
      'Home',
      name: 'text_home',
      desc: '',
      args: [],
    );
  }

  /// `Explore`
  String get text_explore {
    return Intl.message(
      'Explore',
      name: 'text_explore',
      desc: '',
      args: [],
    );
  }

  /// `My Trip`
  String get text_my_trip {
    return Intl.message(
      'My Trip',
      name: 'text_my_trip',
      desc: '',
      args: [],
    );
  }

  /// `Loyalty`
  String get text_loyalty {
    return Intl.message(
      'Loyalty',
      name: 'text_loyalty',
      desc: '',
      args: [],
    );
  }

  /// `Profile`
  String get text_profile {
    return Intl.message(
      'Profile',
      name: 'text_profile',
      desc: '',
      args: [],
    );
  }

  /// `Please enter a valid phone number (9 digits)!`
  String get text_error_phone_9 {
    return Intl.message(
      'Please enter a valid phone number (9 digits)!',
      name: 'text_error_phone_9',
      desc: '',
      args: [],
    );
  }

  /// `Please enter a valid phone number (10 digits)!`
  String get text_error_phone_10 {
    return Intl.message(
      'Please enter a valid phone number (10 digits)!',
      name: 'text_error_phone_10',
      desc: '',
      args: [],
    );
  }

  /// `Waiting for payment`
  String get text_waiting_payment {
    return Intl.message(
      'Waiting for payment',
      name: 'text_waiting_payment',
      desc: '',
      args: [],
    );
  }

  /// `Each account is required to have a TripC ID, so you cannot remove the TripC ID from your account.`
  String get text_cannot_remove_default_tripcid {
    return Intl.message(
      'Each account is required to have a TripC ID, so you cannot remove the TripC ID from your account.',
      name: 'text_cannot_remove_default_tripcid',
      desc: '',
      args: [],
    );
  }

  /// `The order you selected is temporarily out of stock. Please choose another order or try later.`
  String get text_out_of_stock {
    return Intl.message(
      'The order you selected is temporarily out of stock. Please choose another order or try later.',
      name: 'text_out_of_stock',
      desc: '',
      args: [],
    );
  }

  /// `Time`
  String get time {
    return Intl.message(
      'Time',
      name: 'time',
      desc: '',
      args: [],
    );
  }

  /// `Location`
  String get location {
    return Intl.message(
      'Location',
      name: 'location',
      desc: '',
      args: [],
    );
  }

  /// `Introduction`
  String get introduction {
    return Intl.message(
      'Introduction',
      name: 'introduction',
      desc: '',
      args: [],
    );
  }

  /// `Ticket Price`
  String get ticket_price {
    return Intl.message(
      'Ticket Price',
      name: 'ticket_price',
      desc: '',
      args: [],
    );
  }

  /// `Surcharge included`
  String get surcharge_included {
    return Intl.message(
      'Surcharge included',
      name: 'surcharge_included',
      desc: '',
      args: [],
    );
  }

  /// `Order information`
  String get order_information {
    return Intl.message(
      'Order information',
      name: 'order_information',
      desc: '',
      args: [],
    );
  }

  /// `Select date`
  String get select_date {
    return Intl.message(
      'Select date',
      name: 'select_date',
      desc: '',
      args: [],
    );
  }

  /// `Quantity`
  String get quantity {
    return Intl.message(
      'Quantity',
      name: 'quantity',
      desc: '',
      args: [],
    );
  }

  /// `Taller than 1 m`
  String get taller_than_1_meter {
    return Intl.message(
      'Taller than 1 m',
      name: 'taller_than_1_meter',
      desc: '',
      args: [],
    );
  }

  /// `5 – 9 years old`
  String get from_5_to_9_year_old {
    return Intl.message(
      '5 – 9 years old',
      name: 'from_5_to_9_year_old',
      desc: '',
      args: [],
    );
  }

  /// `Baby`
  String get baby {
    return Intl.message(
      'Baby',
      name: 'baby',
      desc: '',
      args: [],
    );
  }

  /// `Below 5 years old`
  String get below_5_year_old {
    return Intl.message(
      'Below 5 years old',
      name: 'below_5_year_old',
      desc: '',
      args: [],
    );
  }

  /// `Children`
  String get children {
    return Intl.message(
      'Children',
      name: 'children',
      desc: '',
      args: [],
    );
  }

  /// `Tuan Chau - Cat Ba ferry route`
  String get tuan_chau_cat_ba_ferry_route {
    return Intl.message(
      'Tuan Chau - Cat Ba ferry route',
      name: 'tuan_chau_cat_ba_ferry_route',
      desc: '',
      args: [],
    );
  }

  /// `Cat Ba - Tuan Chau ferry route`
  String get cat_ba_tuan_chau_ferry_route {
    return Intl.message(
      'Cat Ba - Tuan Chau ferry route',
      name: 'cat_ba_tuan_chau_ferry_route',
      desc: '',
      args: [],
    );
  }

  /// `Use the offer`
  String get use_the_offer {
    return Intl.message(
      'Use the offer',
      name: 'use_the_offer',
      desc: '',
      args: [],
    );
  }

  /// `(You have {content} coupon code, enjoy it now)`
  String you_having_x_discount_code_enjoy(String content) {
    return Intl.message(
      '(You have $content coupon code, enjoy it now)',
      name: 'you_having_x_discount_code_enjoy',
      desc: '',
      args: [content],
    );
  }

  /// `Request for electronic invoice`
  String get request_for_electronic_invoice {
    return Intl.message(
      'Request for electronic invoice',
      name: 'request_for_electronic_invoice',
      desc: '',
      args: [],
    );
  }

  /// `Optional`
  String get optional {
    return Intl.message(
      'Optional',
      name: 'optional',
      desc: '',
      args: [],
    );
  }

  /// `Issue electronic invoices`
  String get issue_electronic_invoice {
    return Intl.message(
      'Issue electronic invoices',
      name: 'issue_electronic_invoice',
      desc: '',
      args: [],
    );
  }

  /// `Invoices for Individuals`
  String get invoices_for_individuals {
    return Intl.message(
      'Invoices for Individuals',
      name: 'invoices_for_individuals',
      desc: '',
      args: [],
    );
  }

  /// `Invoices for Businesses`
  String get invoices_for_businesses {
    return Intl.message(
      'Invoices for Businesses',
      name: 'invoices_for_businesses',
      desc: '',
      args: [],
    );
  }

  /// `Business information`
  String get business_information {
    return Intl.message(
      'Business information',
      name: 'business_information',
      desc: '',
      args: [],
    );
  }

  /// `Enter your business address`
  String get enter_your_business_address {
    return Intl.message(
      'Enter your business address',
      name: 'enter_your_business_address',
      desc: '',
      args: [],
    );
  }

  /// `Enter your business tax code`
  String get enter_your_business_tax_code {
    return Intl.message(
      'Enter your business tax code',
      name: 'enter_your_business_tax_code',
      desc: '',
      args: [],
    );
  }

  /// `Enter your business email`
  String get enter_your_business_email {
    return Intl.message(
      'Enter your business email',
      name: 'enter_your_business_email',
      desc: '',
      args: [],
    );
  }

  /// `Ticket type`
  String get type_ticket {
    return Intl.message(
      'Ticket type',
      name: 'type_ticket',
      desc: '',
      args: [],
    );
  }

  /// `Date`
  String get date {
    return Intl.message(
      'Date',
      name: 'date',
      desc: '',
      args: [],
    );
  }

  /// `Of`
  String get text_of {
    return Intl.message(
      'Of',
      name: 'text_of',
      desc: '',
      args: [],
    );
  }

  /// `Review placed orders`
  String get view_order_history {
    return Intl.message(
      'Review placed orders',
      name: 'view_order_history',
      desc: '',
      args: [],
    );
  }

  /// `Payment with PayOS`
  String get text_payment_with_payos {
    return Intl.message(
      'Payment with PayOS',
      name: 'text_payment_with_payos',
      desc: '',
      args: [],
    );
  }

  /// `Saved QR to your gallery`
  String get text_save_qr_to_gallery {
    return Intl.message(
      'Saved QR to your gallery',
      name: 'text_save_qr_to_gallery',
      desc: '',
      args: [],
    );
  }

  /// `Storage permission denied`
  String get text_storage_permission_denied {
    return Intl.message(
      'Storage permission denied',
      name: 'text_storage_permission_denied',
      desc: '',
      args: [],
    );
  }

  /// `View booked tickets`
  String get text_view_booked_ticket {
    return Intl.message(
      'View booked tickets',
      name: 'text_view_booked_ticket',
      desc: '',
      args: [],
    );
  }

  /// `Full name:`
  String get text_fullname {
    return Intl.message(
      'Full name:',
      name: 'text_fullname',
      desc: '',
      args: [],
    );
  }

  /// `Contact TripC immediately if you want to change your information.`
  String get text_contact_tripc_to_change_your_information {
    return Intl.message(
      'Contact TripC immediately if you want to change your information.',
      name: 'text_contact_tripc_to_change_your_information',
      desc: '',
      args: [],
    );
  }

  /// `Request for electronic invoice`
  String get text_request_for_electronic_invoice {
    return Intl.message(
      'Request for electronic invoice',
      name: 'text_request_for_electronic_invoice',
      desc: '',
      args: [],
    );
  }

  /// `Your ticket list`
  String get text_your_ticket_list {
    return Intl.message(
      'Your ticket list',
      name: 'text_your_ticket_list',
      desc: '',
      args: [],
    );
  }

  /// `QR code for ticket {content}`
  String text_qr_code_ticket(String content) {
    return Intl.message(
      'QR code for ticket $content',
      name: 'text_qr_code_ticket',
      desc: '',
      args: [content],
    );
  }

  /// `Ticket ID: `
  String get text_ticket_id {
    return Intl.message(
      'Ticket ID: ',
      name: 'text_ticket_id',
      desc: '',
      args: [],
    );
  }

  /// `ticket`
  String get ticket {
    return Intl.message(
      'ticket',
      name: 'ticket',
      desc: '',
      args: [],
    );
  }

  /// `Number plate`
  String get number_plate {
    return Intl.message(
      'Number plate',
      name: 'number_plate',
      desc: '',
      args: [],
    );
  }

  /// `Enter number plate`
  String get enter_number_plate {
    return Intl.message(
      'Enter number plate',
      name: 'enter_number_plate',
      desc: '',
      args: [],
    );
  }

  /// `Booking successful`
  String get text_reservation_success {
    return Intl.message(
      'Booking successful',
      name: 'text_reservation_success',
      desc: '',
      args: [],
    );
  }

  /// `Booking failed`
  String get text_reservation_failure {
    return Intl.message(
      'Booking failed',
      name: 'text_reservation_failure',
      desc: '',
      args: [],
    );
  }

  /// `Reservation QR code`
  String get text_qr_code_reservation {
    return Intl.message(
      'Reservation QR code',
      name: 'text_qr_code_reservation',
      desc: '',
      args: [],
    );
  }

  /// `Your reservation code`
  String get text_your_reservation_code {
    return Intl.message(
      'Your reservation code',
      name: 'text_your_reservation_code',
      desc: '',
      args: [],
    );
  }

  /// `Service`
  String get text_service {
    return Intl.message(
      'Service',
      name: 'text_service',
      desc: '',
      args: [],
    );
  }

  /// `Reservate at `
  String get text_reservate_at {
    return Intl.message(
      'Reservate at ',
      name: 'text_reservate_at',
      desc: '',
      args: [],
    );
  }

  /// `Reservation QR`
  String get text_qr_reservation {
    return Intl.message(
      'Reservation QR',
      name: 'text_qr_reservation',
      desc: '',
      args: [],
    );
  }

  /// `Duration`
  String get duration {
    return Intl.message(
      'Duration',
      name: 'duration',
      desc: '',
      args: [],
    );
  }

  /// `Food`
  String get food {
    return Intl.message(
      'Food',
      name: 'food',
      desc: '',
      args: [],
    );
  }

  /// `Health & Beauty`
  String get health_beauty_category {
    return Intl.message(
      'Health & Beauty',
      name: 'health_beauty_category',
      desc: '',
      args: [],
    );
  }

  /// `Event`
  String get event {
    return Intl.message(
      'Event',
      name: 'event',
      desc: '',
      args: [],
    );
  }

  /// `Tour and experience`
  String get tour_and_experience {
    return Intl.message(
      'Tour and experience',
      name: 'tour_and_experience',
      desc: '',
      args: [],
    );
  }

  /// `Have fun`
  String get have_fun {
    return Intl.message(
      'Have fun',
      name: 'have_fun',
      desc: '',
      args: [],
    );
  }

  /// `Golf and sport`
  String get gold_and_sport {
    return Intl.message(
      'Golf and sport',
      name: 'gold_and_sport',
      desc: '',
      args: [],
    );
  }

  /// `Shopping`
  String get shopping {
    return Intl.message(
      'Shopping',
      name: 'shopping',
      desc: '',
      args: [],
    );
  }

  /// `Sort`
  String get sort {
    return Intl.message(
      'Sort',
      name: 'sort',
      desc: '',
      args: [],
    );
  }

  /// `Regulatory Information`
  String get regulatory_information {
    return Intl.message(
      'Regulatory Information',
      name: 'regulatory_information',
      desc: '',
      args: [],
    );
  }

  /// `No deposit required when booking`
  String get no_deposit_required_when_booking {
    return Intl.message(
      'No deposit required when booking',
      name: 'no_deposit_required_when_booking',
      desc: '',
      args: [],
    );
  }

  /// `Our products`
  String get our_products {
    return Intl.message(
      'Our products',
      name: 'our_products',
      desc: '',
      args: [],
    );
  }

  /// `Complete booking`
  String get complete_booking {
    return Intl.message(
      'Complete booking',
      name: 'complete_booking',
      desc: '',
      args: [],
    );
  }

  /// `Booking information`
  String get booking_information {
    return Intl.message(
      'Booking information',
      name: 'booking_information',
      desc: '',
      args: [],
    );
  }

  /// `Reservation date`
  String get reservation_date {
    return Intl.message(
      'Reservation date',
      name: 'reservation_date',
      desc: '',
      args: [],
    );
  }

  /// `Reservation time`
  String get reservation_time {
    return Intl.message(
      'Reservation time',
      name: 'reservation_time',
      desc: '',
      args: [],
    );
  }

  /// `Enter special request...`
  String get enter_special_request {
    return Intl.message(
      'Enter special request...',
      name: 'enter_special_request',
      desc: '',
      args: [],
    );
  }

  /// `Reservation time`
  String get coming_time {
    return Intl.message(
      'Reservation time',
      name: 'coming_time',
      desc: '',
      args: [],
    );
  }

  /// `Number of guests`
  String get guest_quantity {
    return Intl.message(
      'Number of guests',
      name: 'guest_quantity',
      desc: '',
      args: [],
    );
  }

  /// `Number of guests ({content})`
  String guest_quantity_with_count(int content) {
    return Intl.message(
      'Number of guests ($content)',
      name: 'guest_quantity_with_count',
      desc: '',
      args: [content],
    );
  }

  /// `Booking`
  String get booking {
    return Intl.message(
      'Booking',
      name: 'booking',
      desc: '',
      args: [],
    );
  }

  /// `Time`
  String get text_time {
    return Intl.message(
      'Time',
      name: 'text_time',
      desc: '',
      args: [],
    );
  }

  /// `Booking successful`
  String get booking_success {
    return Intl.message(
      'Booking successful',
      name: 'booking_success',
      desc: '',
      args: [],
    );
  }

  /// `Booking failed`
  String get booking_failure {
    return Intl.message(
      'Booking failed',
      name: 'booking_failure',
      desc: '',
      args: [],
    );
  }

  /// `Thank you for your reservation. We will send you a confirmation email shortly. We hope you have a great experience at the restaurant!`
  String get booking_success_dialog_content {
    return Intl.message(
      'Thank you for your reservation. We will send you a confirmation email shortly. We hope you have a great experience at the restaurant!',
      name: 'booking_success_dialog_content',
      desc: '',
      args: [],
    );
  }

  /// `Detail booking`
  String get detail_booking {
    return Intl.message(
      'Detail booking',
      name: 'detail_booking',
      desc: '',
      args: [],
    );
  }

  /// `Back to homepage`
  String get go_back_to_homepage {
    return Intl.message(
      'Back to homepage',
      name: 'go_back_to_homepage',
      desc: '',
      args: [],
    );
  }

  /// `Reservation request has been recorded!`
  String get request_order_recorded {
    return Intl.message(
      'Reservation request has been recorded!',
      name: 'request_order_recorded',
      desc: '',
      args: [],
    );
  }

  /// `The application is in testing phase and is in the process of registration with the Ministry of Industry and Trade`
  String get ministry_of_industry_notice {
    return Intl.message(
      'The application is in testing phase and is in the process of registration with the Ministry of Industry and Trade',
      name: 'ministry_of_industry_notice',
      desc: '',
      args: [],
    );
  }

  /// `I agree to the `
  String get i_agree_with {
    return Intl.message(
      'I agree to the ',
      name: 'i_agree_with',
      desc: '',
      args: [],
    );
  }

  /// `Terms of Use`
  String get term_of_use {
    return Intl.message(
      'Terms of Use',
      name: 'term_of_use',
      desc: '',
      args: [],
    );
  }

  /// `Terms and Privacy Policy`
  String get terms_and_privacy_policy {
    return Intl.message(
      'Terms and Privacy Policy',
      name: 'terms_and_privacy_policy',
      desc: '',
      args: [],
    );
  }

  /// ` of TripC`
  String get of_TripC {
    return Intl.message(
      ' of TripC',
      name: 'of_TripC',
      desc: '',
      args: [],
    );
  }

  /// `Receiving feedback from social organizations`
  String get receive_feedback_social_organization {
    return Intl.message(
      'Receiving feedback from social organizations',
      name: 'receive_feedback_social_organization',
      desc: '',
      args: [],
    );
  }

  /// `List feedbacks from social organizations`
  String get list_feedback_social_organization {
    return Intl.message(
      'List feedbacks from social organizations',
      name: 'list_feedback_social_organization',
      desc: '',
      args: [],
    );
  }

  /// `Name of social organization`
  String get name_of_social_organization {
    return Intl.message(
      'Name of social organization',
      name: 'name_of_social_organization',
      desc: '',
      args: [],
    );
  }

  /// `Enter the name of the social organization`
  String get enter_name_of_social_organization {
    return Intl.message(
      'Enter the name of the social organization',
      name: 'enter_name_of_social_organization',
      desc: '',
      args: [],
    );
  }

  /// `Establishment decision number`
  String get establishment_decision_number {
    return Intl.message(
      'Establishment decision number',
      name: 'establishment_decision_number',
      desc: '',
      args: [],
    );
  }

  /// `Enter the establishment decision number`
  String get enter_establishment_decision_number {
    return Intl.message(
      'Enter the establishment decision number',
      name: 'enter_establishment_decision_number',
      desc: '',
      args: [],
    );
  }

  /// `Content`
  String get content {
    return Intl.message(
      'Content',
      name: 'content',
      desc: '',
      args: [],
    );
  }

  /// `Enter content no more than 250 characters`
  String get enter_content_no_more_than_250_characters {
    return Intl.message(
      'Enter content no more than 250 characters',
      name: 'enter_content_no_more_than_250_characters',
      desc: '',
      args: [],
    );
  }

  /// `Feedback successfully`
  String get feedback_successfully {
    return Intl.message(
      'Feedback successfully',
      name: 'feedback_successfully',
      desc: '',
      args: [],
    );
  }

  /// `The feedback has been updated on TripC's system`
  String get feedback_success_content {
    return Intl.message(
      'The feedback has been updated on TripC\'s system',
      name: 'feedback_success_content',
      desc: '',
      args: [],
    );
  }

  /// `About {content} min ago`
  String min_ago(int content) {
    return Intl.message(
      'About $content min ago',
      name: 'min_ago',
      desc: '',
      args: [content],
    );
  }

  /// `About {content} hour ago`
  String hour_ago(int content) {
    return Intl.message(
      'About $content hour ago',
      name: 'hour_ago',
      desc: '',
      args: [content],
    );
  }

  /// `About {content} days ago`
  String day_ago(int content) {
    return Intl.message(
      'About $content days ago',
      name: 'day_ago',
      desc: '',
      args: [content],
    );
  }

  /// `About {content} month ago`
  String month_ago(int content) {
    return Intl.message(
      'About $content month ago',
      name: 'month_ago',
      desc: '',
      args: [content],
    );
  }

  /// `About {content} year ago`
  String year_ago(int content) {
    return Intl.message(
      'About $content year ago',
      name: 'year_ago',
      desc: '',
      args: [content],
    );
  }

  /// `Suggest for you`
  String get suggest_for_you {
    return Intl.message(
      'Suggest for you',
      name: 'suggest_for_you',
      desc: '',
      args: [],
    );
  }

  /// `Order code`
  String get order_code {
    return Intl.message(
      'Order code',
      name: 'order_code',
      desc: '',
      args: [],
    );
  }

  /// `Every day`
  String get every_day {
    return Intl.message(
      'Every day',
      name: 'every_day',
      desc: '',
      args: [],
    );
  }

  /// `Terms and conditions`
  String get terms_and_conditions {
    return Intl.message(
      'Terms and conditions',
      name: 'terms_and_conditions',
      desc: '',
      args: [],
    );
  }

  /// `Other products`
  String get other_products {
    return Intl.message(
      'Other products',
      name: 'other_products',
      desc: '',
      args: [],
    );
  }

  /// `Serving at`
  String get serving_at {
    return Intl.message(
      'Serving at',
      name: 'serving_at',
      desc: '',
      args: [],
    );
  }

  /// `Work time`
  String get work_time {
    return Intl.message(
      'Work time',
      name: 'work_time',
      desc: '',
      args: [],
    );
  }

  /// `Close`
  String get close {
    return Intl.message(
      'Close',
      name: 'close',
      desc: '',
      args: [],
    );
  }

  /// `Comfirmed`
  String get confirmed {
    return Intl.message(
      'Comfirmed',
      name: 'confirmed',
      desc: '',
      args: [],
    );
  }

  /// `Hotel name...`
  String get hotel_name {
    return Intl.message(
      'Hotel name...',
      name: 'hotel_name',
      desc: '',
      args: [],
    );
  }

  /// `Featured accommodations`
  String get featured_accommodation {
    return Intl.message(
      'Featured accommodations',
      name: 'featured_accommodation',
      desc: '',
      args: [],
    );
  }

  /// `Enter place`
  String get enter_place {
    return Intl.message(
      'Enter place',
      name: 'enter_place',
      desc: '',
      args: [],
    );
  }

  /// `Recent searches`
  String get recent_searches {
    return Intl.message(
      'Recent searches',
      name: 'recent_searches',
      desc: '',
      args: [],
    );
  }

  /// `Refund`
  String get refund {
    return Intl.message(
      'Refund',
      name: 'refund',
      desc: '',
      args: [],
    );
  }

  /// `Total refund amount`
  String get total_refund_amount {
    return Intl.message(
      'Total refund amount',
      name: 'total_refund_amount',
      desc: '',
      args: [],
    );
  }

  /// `Withdrawn amount`
  String get withdrawn_amount {
    return Intl.message(
      'Withdrawn amount',
      name: 'withdrawn_amount',
      desc: '',
      args: [],
    );
  }

  /// `Amount available for withdrawal`
  String get amount_available_for_withdrawal {
    return Intl.message(
      'Amount available for withdrawal',
      name: 'amount_available_for_withdrawal',
      desc: '',
      args: [],
    );
  }

  /// `Refund history`
  String get refund_history {
    return Intl.message(
      'Refund history',
      name: 'refund_history',
      desc: '',
      args: [],
    );
  }

  /// `Room amount (Including Taxes and Fees)`
  String get room_amount_including_taxes_fees {
    return Intl.message(
      'Room amount (Including Taxes and Fees)',
      name: 'room_amount_including_taxes_fees',
      desc: '',
      args: [],
    );
  }

  /// `Estimated refund time`
  String get estimated_refund_time {
    return Intl.message(
      'Estimated refund time',
      name: 'estimated_refund_time',
      desc: '',
      args: [],
    );
  }

  /// `Refund`
  String get refund_text {
    return Intl.message(
      'Refund',
      name: 'refund_text',
      desc: '',
      args: [],
    );
  }

  /// `Withdraw money`
  String get withdraw_money {
    return Intl.message(
      'Withdraw money',
      name: 'withdraw_money',
      desc: '',
      args: [],
    );
  }

  /// `Refunded`
  String get refunded {
    return Intl.message(
      'Refunded',
      name: 'refunded',
      desc: '',
      args: [],
    );
  }

  /// `Cancel refund`
  String get cancel_refund {
    return Intl.message(
      'Cancel refund',
      name: 'cancel_refund',
      desc: '',
      args: [],
    );
  }

  /// `Refund detail`
  String get refund_detail {
    return Intl.message(
      'Refund detail',
      name: 'refund_detail',
      desc: '',
      args: [],
    );
  }

  /// `Order placed`
  String get order_placed {
    return Intl.message(
      'Order placed',
      name: 'order_placed',
      desc: '',
      args: [],
    );
  }

  /// `Tracking information`
  String get tracking_information {
    return Intl.message(
      'Tracking information',
      name: 'tracking_information',
      desc: '',
      args: [],
    );
  }

  /// `Branch`
  String get branch {
    return Intl.message(
      'Branch',
      name: 'branch',
      desc: '',
      args: [],
    );
  }

  /// `Service`
  String get service {
    return Intl.message(
      'Service',
      name: 'service',
      desc: '',
      args: [],
    );
  }

  /// `Order time`
  String get order_time {
    return Intl.message(
      'Order time',
      name: 'order_time',
      desc: '',
      args: [],
    );
  }

  /// `Total amount`
  String get total_amount {
    return Intl.message(
      'Total amount',
      name: 'total_amount',
      desc: '',
      args: [],
    );
  }

  /// `Refunded amount`
  String get refunded_amount {
    return Intl.message(
      'Refunded amount',
      name: 'refunded_amount',
      desc: '',
      args: [],
    );
  }

  /// `Image uploaded`
  String get image_uploaded {
    return Intl.message(
      'Image uploaded',
      name: 'image_uploaded',
      desc: '',
      args: [],
    );
  }

  /// `Minimum withdrawal amount`
  String get minimum_withdrawal_amount {
    return Intl.message(
      'Minimum withdrawal amount',
      name: 'minimum_withdrawal_amount',
      desc: '',
      args: [],
    );
  }

  /// `Maximum withdrawal amount per day`
  String get maximum_withdrawal_amount_per_day {
    return Intl.message(
      'Maximum withdrawal amount per day',
      name: 'maximum_withdrawal_amount_per_day',
      desc: '',
      args: [],
    );
  }

  /// `Withdrawal history`
  String get withdrawal_history {
    return Intl.message(
      'Withdrawal history',
      name: 'withdrawal_history',
      desc: '',
      args: [],
    );
  }

  /// `{content} đêm`
  String night_with_count(int content) {
    return Intl.message(
      '$content đêm',
      name: 'night_with_count',
      desc: '',
      args: [content],
    );
  }

  /// `Description`
  String get description {
    return Intl.message(
      'Description',
      name: 'description',
      desc: '',
      args: [],
    );
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en'),
      Locale.fromSubtags(languageCode: 'vi'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<S> load(Locale locale) => S.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
