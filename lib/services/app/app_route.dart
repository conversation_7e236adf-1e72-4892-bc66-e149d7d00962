import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:persistent_bottom_nav_bar_v2/persistent_bottom_nav_bar_v2.dart';
import 'package:tripc_app/models/app/booking_detail.dart';
import 'package:tripc_app/pages/authencation/views/tripc_forgot_pass.dart';
import 'package:tripc_app/pages/authencation/views/tripc_sign_in.dart';
import 'package:tripc_app/pages/authencation/views/tripc_sign_up.dart';
import 'package:tripc_app/pages/booking/components/tripc_list_categories_bookings_view.dart';
import 'package:tripc_app/pages/booking/views/trip_c_booking_detail.dart';
import 'package:tripc_app/pages/booking/views/trip_c_booking_search_view.dart';
import 'package:tripc_app/pages/booking/views/trip_c_product_detail.dart';
import 'package:tripc_app/pages/homepage/views/tripc_homepage.dart';
import 'package:tripc_app/pages/homepage/views/tripc_list_tours_view.dart';
import 'package:tripc_app/pages/homepage/views/tripc_list_categories_tours_view.dart';
import 'package:tripc_app/pages/manage_profile/views/tripc_input_information_page.dart';
import 'package:tripc_app/pages/manage_profile/views/tripc_link_email_page.dart';
import 'package:tripc_app/pages/manage_profile/views/tripc_link_phone_number_page.dart';
import 'package:tripc_app/pages/manage_profile/views/tripc_manage_profile_page.dart';
import 'package:tripc_app/pages/manage_profile/views/tripc_reset_password_page.dart';
import 'package:tripc_app/pages/membership/views/membership_code_view.dart';
import 'package:tripc_app/pages/membership/views/membership_forgot_passcode.dart';
import 'package:tripc_app/pages/membership/views/membership_good_number_see_more.dart';
import 'package:tripc_app/pages/membership/views/membership_payment_success.dart';
import 'package:tripc_app/pages/membership/views/membership_payment_view.dart';
import 'package:tripc_app/pages/membership/views/membership_scan_qr.dart';
import 'package:tripc_app/pages/membership/views/membership_select_good_number_view.dart';
import 'package:tripc_app/pages/membership/views/membership_tripcId_result.dart';
import 'package:tripc_app/pages/membership/views/membership_tripc_sign_in.dart';
import 'package:tripc_app/pages/membership/views/membership_type_passcode.dart';
import 'package:tripc_app/pages/my_trip/views/my_trip_detail_page.dart';
import 'package:tripc_app/pages/my_trip/views/my_trip_detail_page_v2.dart';
import 'package:tripc_app/pages/my_trip/views/ticket_info_view.dart';
import 'package:tripc_app/pages/my_trip/views/ticket_infor_view_v2.dart';
import 'package:tripc_app/pages/notifications/views/tripc_detailed_notification_page.dart';
import 'package:tripc_app/pages/notifications/views/tripc_notification_page.dart';
import 'package:tripc_app/pages/profile/tripc_profile.dart';
import 'package:tripc_app/pages/my_trip/views/my_trip_main_page.dart';
import 'package:tripc_app/pages/profile/views/country/tripc_setting_country.dart';
import 'package:tripc_app/pages/profile/views/currency/tripc_setting_currency.dart';
import 'package:tripc_app/pages/profile/views/language/tripc_setting_language.dart';
import 'package:tripc_app/pages/profile/views/list_tripcId/tripc_profile_list_tripcId.dart';
import 'package:tripc_app/pages/profile/views/list_tripcId/tripc_profile_tripcId_setting.dart';
import 'package:tripc_app/pages/profile/views/setting/tripc_profile_setting.dart';
import 'package:tripc_app/pages/search_page/views/tripc_search_page.dart';
import 'package:tripc_app/pages/ticket_tour/views/tripc_add_passenger_quantity_view.dart';
import 'package:tripc_app/pages/ticket_tour/views/tripc_add_passenger_quantity_view_2.dart';
import 'package:tripc_app/pages/ticket_tour/views/tripc_add_passenger_view.dart';
import 'package:tripc_app/pages/ticket_tour/views/tripc_edit_passenger_view.dart';
import 'package:tripc_app/pages/ticket_tour/views/tripc_input_passenger_information.dart';
import 'package:tripc_app/pages/ticket_tour/views/tripc_search_entertainment_view.dart';
import 'package:tripc_app/pages/ticket_tour/views/tripc_search_ticket_view.dart';
import 'package:tripc_app/pages/splash/views/tripc_splash_page.dart';
import 'package:tripc_app/pages/tabbar/app_tabbar.dart';
import 'package:tripc_app/pages/ticket_tour/views/tripc_tour_detail_view.dart';
import 'package:tripc_app/pages/ticket_tour/views/tripc_tour_detail_view_v2.dart';
import 'package:tripc_app/pages/tour-payment/views/electronic_invoice_v2.dart';
import 'package:tripc_app/pages/tour-payment/views/payment_page.dart';
import 'package:tripc_app/pages/tour-payment/views/payment_page_v2.dart';
import 'package:tripc_app/pages/tour-payment/views/payment_payos_webview.dart';
import 'package:tripc_app/pages/tour-payment/views/payment_promotional_code_page.dart';
import 'package:tripc_app/pages/tour-payment/views/payment_success_page.dart';
import 'package:tripc_app/pages/tutorial/view/tripc_tutorial.dart';
import 'package:tripc_app/pages/webview/views/tripc_webview.dart';
import 'package:tripc_app/utils/app_arguments_screen.dart';
import 'package:tripc_app/utils/app_enum.dart';
import 'package:tripc_app/widgets/commons/comming_soon/tripc_chat_comming_soon.dart';
import 'package:tripc_app/widgets/commons/tripc_verify_otp/tripc_verify_otp.dart';
import 'package:tripc_app/pages/stay/stay_export.dart';

import '../../pages/booking/views/booking_add_passenger_quantity_view.dart';
import '../../pages/booking/views/booking_detail_order_page.dart';
import '../../pages/booking/views/booking_electric_invoice_view.dart';
import '../../pages/booking/views/booking_payment_view.dart';
import '../../pages/no_internet/no_internet_screen.dart';
import '../../pages/profile/views/feedback/list_feedback_view.dart';
import '../../pages/profile/views/feedback/receiving_feedback_view.dart';
import '../../pages/profile/views/list_tripcId/tripc_profile_reset_passcode.dart';
import '../../pages/profile/views/list_tripcId/tripc_profile_verify_otp_passcode.dart';
import '../../pages/profile/views/privacy&policy/tripc_profile_introduce_tripc_ai.dart';
import '../../pages/profile/views/privacy&policy/tripc_profile_privacy.dart';
import '../../pages/profile/views/privacy&policy/tripc_profile_term_and_condition.dart';
import '../../pages/profile/views/saved_tours/tripc_profile_saved_tour.dart';
import '../../pages/booking/views/booking_ticket_page.dart';
import '../../pages/tour-payment/views/electronic_invoice.dart';
import '../../pages/my_trip/views/my_trip_confirm_refund.dart';
import '../../pages/my_trip/views/my_trip_refund_page.dart';
import '../../pages/my_trip/views/my_trip_refund_reason_page.dart';
import '../../pages/profile/views/recently_viewed/tripc_profile_recently_viewed.dart';
import '../../pages/rating/views/rating_page.dart';
import '../../pages/profile/views/contact/tripc_profile_add_contact.dart';
import '../../pages/profile/views/contact/tripc_profile_contact.dart';
import '../../pages/profile/views/contact/tripc_profile_edit_contact.dart';
import '../../utils/app_log.dart';

class AppRoute {
  factory AppRoute() => _instance;

  AppRoute._private();

  ///#region ROUTE NAMES
  /// -----------------
  static const String routeRoot = '/';
  static const String routeSignIn = '/route-sign-in';
  static const String routeSignUp = '/route-sign-up';
  static const String routeForgotPass = '/route-forgot-pass';
  static const String routeSplashScreen = '/route-splash-screen';
  static const String routeTutorial = '/route-tutorial';
  static const String routeMembershipCode = '/route-membership-code';
  static const String routeMembershipSignIn = '/route-membership-sign-in';
  static const String routeMembershipForgotPass =
      '/route-membership-forgot-pass';
  static const String routeMembershipTripcIdResult =
      '/route-membership-tripc-id-result';
  static const String routeMembershipGoodNumber =
      '/route-membership-good-number';
  static const String routeSelectGoodNumber = '/route-select-good-number';
  static const String routePaymentNumber = '/route-payment-number';
  static const String routeScanQr = '/route-scan-qr';
  static const String routePaymentSuccess = '/route-payment-success';
  static const String routeTypePassCode = '/route-type-pass-code';
  static const String routeHome = '/route-home';
  static const String routeHomePage = '/route-home-page';
  static const String routeSearchTicket = '/route-search-ticket';
  static const String routeEntertainment = '/route-entertainment';
  static const String routeMoving = '/route-moving';
  static const String routeTourDetailView = '/route-tour-detail-view';
  static const String routeTourDetailViewV2 = '/route-tour-detail-view-v2';
  static const String routeAddPassengerQuantity =
      '/route-add-passengers-quantity';
  static const String routeAddPassengerQuantityV2 =
      '/route-add-passengers-quantity-v2';
  static const String routeAddPassengers = '/route-add-passengers';
  static const String routeInputPassenger = '/route-input-passengers';
  static const String routeEditPassenger = '/route-edit-passengers';
  static const String routeTourPayment = '/route-tour-payment';
  static const String routeTourPaymentV2 = '/route-tour-payment-v2';
  static const String routeTourPaymentSuccess = '/route-tour-payment-success';
  static const String routeTripcPromotionalCode = '/route-promotional-code';
  static const String routeProfilePage = '/route-profile-page';
  static const String routeMyTripPage = '/route-my-trip-page';
  static const String routeMyTripDetailedTour = '/route-my-trip-detailed-tour';
  static const String routeMyTripDetailedTourV2 =
      '/route-my-trip-detailed-tour-v2';
  static const String routeProfileSetting = '/route-profile-setting';
  static const String routeSettingLanguage = '/route-setting-language';
  static const String routeSettingCountry = '/route-setting-country';
  static const String routeSettingCurrency = '/route-setting-currency';
  static const String routeSearchPage = '/route-search-page';
  static const String routeManageProfile = '/route-manage-profile';
  static const String routeInputProfile = '/route-input-profile';
  static const String routeListTripcID = '/route-list-tripcID';
  static const String routeSavedTour = '/route-saved-tour';
  static const String routeListContact = '/route-list-contact';
  static const String routeAddContact = '/route-add-contact';
  static const String routeEditContact = '/route-edit-contact';

  static const String routeTripcIDSetting = '/route-tripcID-setitng';
  static const String routeTripcIDResetPasscode =
      '/route-tripcID-reset-passcode';
  static const String routeRecentlyViewed = '/route-recently-viewed';
  static const String routeGoodNumberSeemore = '/route-good-number-see-more';
  static const String routeTripcVerifyOtp = '/route-tripc-verify-otp';
  static const String routeTripcProfileVerifyOtpPasscode =
      '/route-tripc-profile-verify-otp-passcode';
  static const String routeTripcProfileResetPasscode =
      '/route-tripc-profile-reset-passcode';
  static const String routeResetPassWord = '/route-reset-password';
  static const String routeChatCommingSoon = '/route-chat-comming-soon';
  static const String routeTripcLinkPhoneNumber = '/route-tripc-link-phone';
  static const String routeTripcLinkEmail = '/route-tripc-link-email';
  static const String routeListToursView = '/route-list-tours-view';
  static const String routeListCategoriesToursView =
      '/route-list-categories-tours-view';
  static const String routeListBookingView =
      '/route-list-categories-booking-view';
  static const String routeNotificationPage = '/route-notification-page';
  static const String routeNotificationDetailedPage =
      '/route-notification-detailed-page';
  static const String routeMyTripRefund = '/route-my-trip-refund-page';
  static const String routeMyTripRefundReason =
      '/route-my-trip-refund-reason-page';
  static const String routeMyTripConfirmRefund =
      '/route-my-trip-confirm-refund-page';
  static const String routeRating = '/route-rating';
  static const String routeElectronicInvoice = '/electronic-invoice';
  static const String routeElectronicInvoiceV2 = '/electronic-invoice-v2';
  static const String routeTripcWebView = '/route-tripc-web-view';
  static const String routeNoInternet = 'no-internet';
  static const String routeTicketInfo = '/ticket-info';
  static const String routeTicketInfoV2 = '/ticket-info-v2';
  static const String routeProfilePrivacy = '/route-profile-privacy';
  static const String routeProfileTermAndCondition =
      'route-profile-term-and-condition';
  static const String routeProfileIntroduceTripcAI =
      'route-profile-introduce-tripc-ai';
  static const String routePayOSWebView = '/route-payos-webview';
  static const String routeReceivingFeedback = '/route-receiving-feedback';
  static const String routeListFeedback = '/route-list-feedback';

  static const String routeBookingDetailOrder = '/route-booking-detail-order';
  static const String routeBookingTicket = '/booking-ticket';
  static const String routeBookingAddPassengerQuantity =
      '/route-booking-add-passenger-quantity';
  static const String routeBookingElectricInvoice =
      '/route-booking_electric-invoice';
  static const String routeBooking = 'route-booking';
  static const String routeRestaurantDetailView = 'route-detail-booking';
  static const String routeProductDetailView = 'route-product-detail';
  static const String routeBookingPayment = 'route-booking-payment';
  static const String routeStay = 'route-stay';
  static const String routeStayDetail = 'route-stay-detail';
  static const String routeStaySearch = 'route-stay-search';
  static const String routeStayRefund = 'route-stay-refund';
  static const String routeStayRefundDetail = 'route-stay-refund-detail';

  ///#endregion

  static final AppRoute _instance = AppRoute._private();

  static AppRoute get I => _instance;
  String currentRoute = '';

  /// App route observer
  final MyRouteObserver routeObserver = MyRouteObserver();

  /// App global navigator key
  final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  /// Get app context
  BuildContext? get appContext => navigatorKey.currentContext;

  /// Generate route for app here
  Route<dynamic> generateRoute({
    required RouteSettings settings,
    required bool isAuthenticated,
  }) {
    currentRoute = settings.name ?? '';
    switch (settings.name) {
      case routeSplashScreen:
        return MaterialPageRoute<dynamic>(
          settings: const RouteSettings(name: routeSplashScreen),
          builder: (_) => const SplashScreen(),
        );
      case routeSignIn:
        return MaterialPageRoute<dynamic>(
          settings: const RouteSettings(name: routeSignIn),
          builder: (_) => const TripcSignInPage(),
        );
      case routeSignUp:
        return MaterialPageRoute<dynamic>(
          settings: const RouteSettings(name: routeSignUp),
          builder: (_) => const TripcSignUpPage(),
        );
      case routeForgotPass:
        return MaterialPageRoute<dynamic>(
          settings: const RouteSettings(name: routeForgotPass),
          builder: (_) => const TripcForgotPass(),
        );
      case routeTutorial:
        return MaterialPageRoute<dynamic>(
          settings: const RouteSettings(name: routeTutorial),
          builder: (_) => const TutorialScreen(),
        );
      case routeMembershipCode:
        return MaterialPageRoute<dynamic>(
          settings: const RouteSettings(name: routeTutorial),
          builder: (_) => const MembershipCodeView(),
        );
      case routeMembershipSignIn:
        return MaterialPageRoute<dynamic>(
          settings: const RouteSettings(name: routeTutorial),
          builder: (_) => const MembershipTripCSignIn(),
        );
      case routeMembershipForgotPass:
        return MaterialPageRoute<dynamic>(
          settings: const RouteSettings(name: routeTutorial),
          builder: (_) => const MemberShipForgotPass(),
        );
      case routeMembershipTripcIdResult:
        return MaterialPageRoute<dynamic>(
          settings: const RouteSettings(name: routeTutorial),
          builder: (_) => const MembershipTripCIDResult(),
        );
      case routeSelectGoodNumber:
        return MaterialPageRoute<dynamic>(
            settings: const RouteSettings(name: routeTutorial),
            builder: (_) => const TripcSelectGoodNumber());
      case routePaymentNumber:
        return MaterialPageRoute<dynamic>(
            settings: const RouteSettings(name: routeTutorial),
            builder: (_) => const MembershipPaymentView());
      case routeScanQr:
        return MaterialPageRoute<dynamic>(
            settings: const RouteSettings(name: routeTutorial),
            builder: (_) => const MembershipScanQrView());
      case routePaymentSuccess:
        return MaterialPageRoute<dynamic>(
            settings: const RouteSettings(name: routeTutorial),
            builder: (_) => const MembershipPaymentSuccess());
      case routeTypePassCode:
        return MaterialPageRoute<dynamic>(
            settings: const RouteSettings(name: routeTutorial),
            builder: (_) => const MembershipTypePassCode());
      case routeHome:
        return MaterialPageRoute<dynamic>(
          settings: const RouteSettings(name: routeHome),
          builder: (_) => AppBottomNav(
            argument: settings.arguments as AppTabBarArgument?,
          ),
        );
      case routeHomePage:
        return MaterialPageRoute<dynamic>(
            settings: const RouteSettings(name: routeTutorial),
            builder: (_) => const TripcHomepage());
      // case routeSearchTicket:
      //   return MaterialPageRoute<dynamic>(
      //       settings: const RouteSettings(name: routeTutorial),
      //       builder: (_) => TripcSearchTicketView(category: arguments));
      case routeListTripcID:
        return MaterialPageRoute<dynamic>(
            settings: const RouteSettings(name: routeListTripcID),
            builder: (_) => const TripcProfileListTripcID());
      case routeListContact:
        return MaterialPageRoute<dynamic>(
            settings: const RouteSettings(name: routeListContact),
            builder: (_) => const TripcProfileListContact());
      case routeAddContact:
        return MaterialPageRoute<dynamic>(
            settings: const RouteSettings(name: routeAddContact),
            builder: (_) => const TripcProfileAddContact());
      case routeManageProfile:
        return MaterialPageRoute<dynamic>(
          settings: const RouteSettings(name: routeManageProfile),
          builder: (_) => const TripcManageProfilePage(),
        );
      case routeTripcWebView:
        return MaterialPageRoute<dynamic>(
          settings: const RouteSettings(name: routeManageProfile),
          builder: (_) => const TripcWebview(),
        );
      default:
        return MaterialPageRoute<dynamic>(
          settings: settings,
          builder: (_) => const TripcSignInPage(),
        );
    }
  }

  static Widget _getScreen(String routeName, dynamic arguments) {
    switch (routeName) {
      case routeSignIn:
        return const TripcSignInPage();
      case routeSignUp:
        return const TripcSignUpPage();
      case routeForgotPass:
        return const TripcForgotPass();
      case routeSplashScreen:
        return const SplashScreen();
      case routeTutorial:
        return const TutorialScreen();
      case routeMembershipCode:
        return const MembershipCodeView();
      case routeMembershipSignIn:
        return MembershipTripCSignIn(isExistTripcIdDefault: arguments);
      case routeMembershipForgotPass:
        return const MemberShipForgotPass();
      case routeMembershipTripcIdResult:
        return const MembershipTripCIDResult();
      case routeSelectGoodNumber:
        return const TripcSelectGoodNumber();
      case routePaymentNumber:
        return const MembershipPaymentView();
      case routeScanQr:
        return const MembershipScanQrView();
      case routePaymentSuccess:
        return MembershipPaymentSuccess(isPaymentSuccess: arguments);
      case routeTypePassCode:
        return MembershipTypePassCode(isFromProfile: arguments);
      case routeHomePage:
        return const TripcHomepage();
      case routeSearchTicket:
        return TripcSearchTicketView(category: arguments);
      case routeEntertainment:
        return TripcSearchEntertainmentView(category: arguments);
      case routeBooking:
        return TripCBookingSearchView(category: arguments);
      case routeTourDetailView:
        return TripcTourDetailView(tourId: arguments);
      case routeRestaurantDetailView:
        return TripCBookingDetail(
          arguments['category'],
          restaurantId: arguments['id'],
        );
      case routeProductDetailView:
        return TripCProductDetail(
          arguments['category'],
          productId: arguments['id'],
        );
      case routeTourDetailViewV2:
        return TripcTourDetailViewV2(
          tourId: arguments['id'],
          isFerryTour: arguments?['isFerryTour'],
          fromListType: arguments['listType'],
        );
      case routeAddPassengerQuantity:
        return TripcAddPassengerQuantityView(tour: arguments);
      case routeAddPassengerQuantityV2:
        return TripcAddPassengerQuantityViewV2(
            tour: arguments['tour'], isFerryTour: arguments['isFerryTour']);
      case routeAddPassengers:
        return const TripcAddPassenger();
      case routeEditPassenger:
        return TripcEditPassenger(
          passenger: arguments,
        );
      case routeInputPassenger:
        return TripcInputPassengerInformation(tour: arguments);
      case routeTourPayment:
        return const TripcPaymentPage();
      case routeTourPaymentV2:
        return TripcPaymentPageV2(
            tour: arguments['tour'], isFerryTour: arguments['isFerryTour']);
      case routeTourPaymentSuccess:
        return TripcTourPaymenSuccess(isPaymentSuccess: arguments);
      case routeTripcPromotionalCode:
        return const TripcPromotionalCodePage();
      case routeProfilePage:
        return const TripcProfilePage();
      case routeMyTripPage:
        return const TripcMyTripPage();
      case routeMyTripDetailedTour:
        final bookingDetail = arguments as BookingDetail;
        return TripcMyTripDetailedPage(
          orderId: bookingDetail.orderId,
          isElectronicInvoice: bookingDetail.isElectronicInvoice,
        );
      case routeMyTripDetailedTourV2:
        final bookingDetail = arguments as BookingDetail;
        return TripcMyTripDetailedPageV2(
          orderId: bookingDetail.orderId,
        );
      case routeProfileSetting:
        return const TripcProfileSetting();
      case routeSettingLanguage:
        return const TripcSettingLanguage();
      case routeSettingCountry:
        return const TripcSettingCountry();
      case routeSettingCurrency:
        return const TripcSettingCurrency();
      case routeSearchPage:
        return const TripcSearchPage();
      case routeManageProfile:
        return const TripcManageProfilePage();
      case routeInputProfile:
        return const TripcInputInformationPage();
      case routeHome:
        return const AppBottomNav();
      case routeListTripcID:
        return const TripcProfileListTripcID();
      case routeSavedTour:
        return const TripcProfileSavedTour();
      case routeListContact:
        return const TripcProfileListContact();
      case routeAddContact:
        return const TripcProfileAddContact();
      case routeEditContact:
        return TripcProfileEditContact(contact: arguments);
      case routeTripcIDSetting:
        return TripcProfileTripcIDSetting(
          membershipId: arguments,
        );
      // case routeTripcIDResetPasscode:
      //   return const TripcIDResetPassCode();
      case routeRecentlyViewed:
        return const TripcProfileRecentlyViewed();
      case routeGoodNumberSeemore:
        return TripcGoodNumberSeeMore(
          type: arguments,
        );
      case routeTripcVerifyOtp:
        return const TripcVerifyOtp();
      case routeResetPassWord:
        return const TripcResetPasswordPage();
      case routeTripcProfileVerifyOtpPasscode:
        return const TripcProfileVerifyOtpPasscode();
      case routeTripcProfileResetPasscode:
        return const TripcProfileResetPasscode();
      case routeChatCommingSoon:
        return const TripcChatCommingSoon();
      case routeTripcLinkPhoneNumber:
        return const TripcLinkPhoneNumberPage();
      case routeTripcLinkEmail:
        return const TripcLinkEmailPage();
      case routeListToursView:
        return TripcListToursView(
          category: arguments['category'],
          categoryId: arguments['categoryParent'],
        );
      case routeListCategoriesToursView:
        return TripcListCategoriesToursView(
          id: arguments['id'],
          name: arguments['name'],
          ferryType: FerryTypeHelpers.fromIndex(arguments['ferryTypeIndex']),
          fromListType: arguments['listType'],
        );
      case routeListBookingView:
        return TripcListCategoriesBookingsView(
          id: arguments['id'],
          name: arguments['name'],
          category: arguments['category'],
        );
      case routeNotificationPage:
        return const TripcNotificationPage();
      case routeNotificationDetailedPage:
        return TripcDetailedNotificationPage(notification: arguments);
      case routeMyTripRefund:
        return TripcMyTripRefundPage(
          payment: arguments,
        );
      case routeMyTripRefundReason:
        return const TripcMyTripRefundReasonPage();
      case routeMyTripConfirmRefund:
        return TripcMyTripConfirmRefundPage(
          total: arguments,
        );
      case routeRating:
        return const TripcRatingPage();
      case routeElectronicInvoice:
        if (arguments is List) {
          return ElectronicInvoice(
            isDisableResetInvoice: arguments[0],
            invoice: arguments[1],
          );
        } else {
          return ElectronicInvoice(
            isDisableResetInvoice: arguments,
            invoice: null,
          );
        }
      case routeElectronicInvoiceV2:
        return const ElectronicInvoiceV2();
      case routeTripcWebView:
        final webviewData = arguments as WebViewDataModel;
        return TripcWebview(
            linkUrl: webviewData.linkUrl, title: webviewData.title);
      case routeNoInternet:
        return const NoInternetScreen();
      case routeTicketInfo:
        return TicketInfoView(customerTicket: arguments);
      case routeTicketInfoV2:
        return TicketInforViewV2(order: arguments);
      case routeProfilePrivacy:
        return const TripcProfilePrivacy();
      case routeProfileTermAndCondition:
        return const TripcProfileTermsAndConditions();
      case routeProfileIntroduceTripcAI:
        return const TripcProfileIntroduceTripcAI();
      case routePayOSWebView:
        return PaymentPayOSWebview(
          paymentUrl: arguments,
        );
      case routeBookingDetailOrder:
        return BookingDetailOrderPage(orderId: arguments);
      case routeBookingTicket:
        return BookingTicketPage(
          orderDetail: arguments,
        );
      case routeBookingAddPassengerQuantity:
        return TripcBookingAddPassengerQuantityView(
          seatProductId: arguments,
        );
      case routeBookingPayment:
        return TripcBookingPaymentView(
          supplier: arguments,
        );
      case routeBookingElectricInvoice:
        return const BookingElectricInvoiceView();
      case routeReceivingFeedback:
        return const TripcReceivingFeedback();
      case routeListFeedback:
        return const TripcListFeedback();
      case routeStay:
        return const TripcStaySearchView();
      case routeStaySearch:
        return const TripCSearchDetailScreen();
      case routeStayRefund:
        return const TripCStayRefundScreen();
      case routeStayRefundDetail:
        return TripCStayRefundDetailScreen(
          refundId: arguments,
        );
      case routeStayDetail:
        return TripCStayDetailScreen(
          hotelId: arguments,
        );
      default:
        return const Wrap();
    }
  }

  static void pushNamed(
    BuildContext context, {
    required String routeName,
    dynamic arguments,
    PageTransitionAnimation? pageTransitionAnimation,
    bool? withNavBar,
    PageRoute<dynamic>? customPageRoute,
  }) {
    AppRoute.I.currentRoute = routeName;
    pushScreen(
      context,
      settings: RouteSettings(name: routeName, arguments: arguments),
      screen: _getScreen(routeName, arguments),
      withNavBar: withNavBar ?? false,
      pageTransitionAnimation:
          pageTransitionAnimation ?? PageTransitionAnimation.cupertino,
      customPageRoute: customPageRoute,
    );
  }

  static void pushReplacement(
    BuildContext context, {
    required String routeName,
    dynamic arguments,
  }) {
    AppRoute.I.currentRoute = routeName;
    pushReplacementWithoutNavBar(
      context,
      MaterialPageRoute<dynamic>(
        settings: RouteSettings(name: routeName, arguments: arguments),
        builder: (_) => _getScreen(routeName, arguments),
      ),
    );
  }

  static void pushWithNavBarScreen(
    BuildContext context, {
    required String routeName,
    dynamic arguments,
  }) {
    pushWithNavBar(
        context,
        MaterialPageRoute<dynamic>(
          settings: RouteSettings(name: routeName, arguments: arguments),
          builder: (_) => _getScreen(routeName, arguments),
        ));
  }

  static Future<void> navigateToRoute(
    BuildContext context, {
    required String routeName,
    ValueChanged<Object?>? resultHandler,
    dynamic arguments,
    PageTransitionAnimation? pageTransitionAnimation,
    bool? withNavBar,
    PageRoute<dynamic>? customPageRoute,
  }) async {
    final result = await pushScreen(
      context,
      settings: RouteSettings(name: routeName, arguments: arguments),
      screen: _getScreen(routeName, arguments),
      withNavBar: withNavBar ?? true,
      pageTransitionAnimation:
          pageTransitionAnimation ?? PageTransitionAnimation.cupertino,
      customPageRoute: customPageRoute,
    );
    resultHandler?.call(result);
  }

  static void navigateWithFade(
      String routeName, BuildContext context, dynamic arguments) {
    Navigator.of(context).pushReplacement(
      PageRouteBuilder(
        settings: RouteSettings(name: routeName, arguments: arguments),
        pageBuilder: (context, animation, secondaryAnimation) =>
            _getScreen(routeName, arguments),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(
            opacity: animation,
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 500), // Fade duration
      ),
    );
  }
}

class MyRouteObserver extends RouteObserver<PageRoute<dynamic>> {
  final List<String> _routeStack = [];

  List<String> get routeStack => _routeStack;
  WidgetRef? ref;

  @override
  void didPush(Route route, Route? previousRoute) {
    super.didPush(route, previousRoute);
    if (route is PageRoute) {
      _routeStack.add(route.settings.name ?? '');
      logger.d("Navigated to: ${route.settings.name}");
    }
  }

  @override
  void didPop(Route route, Route? previousRoute) {
    super.didPop(route, previousRoute);
    if (route is PageRoute) {
      _routeStack.remove(route.settings.name);
      logger.d("Popped route: ${route.settings.name}");
    }
  }

  @override
  void didRemove(Route route, Route? previousRoute) {
    super.didRemove(route, previousRoute);
    if (route is PageRoute) {
      _routeStack.remove(route.settings.name);
      logger.d("Removed route: ${route.settings.name}");
    }
  }

  @override
  void didReplace({Route? newRoute, Route? oldRoute}) {
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
    if (oldRoute is PageRoute) {
      _routeStack.remove(oldRoute.settings.name);
    }
    if (newRoute is PageRoute) {
      _routeStack.add(newRoute.settings.name ?? '');
      logger.d(
          "Replaced route: ${oldRoute?.settings.name} ➝ ${newRoute.settings.name}");
    }
  }

  bool isContainRoute(String routeName) {
    return _routeStack.contains(routeName);
  }
}
