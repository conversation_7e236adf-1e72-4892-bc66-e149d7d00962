import 'package:flutter/material.dart';
import 'package:flutter_portal/flutter_portal.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/models/app/tripc_service_category.dart';
import 'package:tripc_app/models/remote/api_tour_response/tour_response.dart';
import 'package:tripc_app/pages/profile/components/tripc_not_yet_login_dialog.dart';
import 'package:tripc_app/pages/homepage/homepage_export.dart';
import 'package:tripc_app/pages/ticket_tour/components/tour_saved_view.dart';
import 'package:tripc_app/pages/ticket_tour/components/tour_ticket_detail_view_v2.dart';
import 'package:tripc_app/pages/ticket_tour/providers/ticket_tour_provider.dart';
import 'package:tripc_app/pages/ticket_tour/providers/ticket_tour_provider_v2.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_portal/tripc_portal.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import '../../../models/app/tour_type_enum.dart';
import '../../../status_display_notifier.dart';
import '../../../widgets/app_show_api_error_dialog.dart';
import '../../homepage/providers/providers.dart';
import '../components/components.dart';

class TripcTourDetailViewV2 extends ConsumerStatefulWidget {
  const TripcTourDetailViewV2({super.key, required this.tourId, this.isFerryTour = false, required this.fromListType});

  final int tourId;
  final bool isFerryTour;
  final ListType fromListType;

  @override
  ConsumerState<TripcTourDetailViewV2> createState() =>
      _TripcTourDetailViewState();
}

class _TripcTourDetailViewState extends ConsumerState<TripcTourDetailViewV2> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      ref.read(pTicketTourProvider.notifier).resetDetailedPage();
      ref
          .read(pTicketTourProvider.notifier)
          .getDetailedTour(tourId: widget.tourId);
      ref.listenManual(
          pTicketTourProvider.select((value) => value.errorMessage),
          (_, error) {
        if (error == null || error.isEmpty) {
          return;
        }
        dialogHelpers.show(context, child: ErrorDialog(text: error));
      });

      ref.listenManual(
          pTicketTourProvider.select((value) => value.isSavedTourSuccess),
          (_, state) {
        if (!state) {
          return;
        }
        final isSave = ref.watch(pTicketTourProvider).isSave;
        ref
            .read(pHomepageScreenProvider.notifier)
            .updateTourResponse(widget.tourId, isSave);
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final selectedTour =
        ref.watch(pTicketTourProvider.select((value) => value.selectedTour));
    final selectButtonEnable = ref
        .watch(pTicketTourProvider.select((value) => value.selectButtonEnable));
    final visiblePortal =
        ref.watch(pTicketTourProvider.select((value) => value.visiblePortal));
    final isSave =
        ref.watch(pTicketTourProvider.select((value) => value.isSave));
    return TripcScaffold(
      onPressed: () => unfocusKeyboard(),
      onPopScope: () =>
          ref.read(pTicketTourProvider.notifier).resetDetailedPage(),
      extendBodyBehindAppBar: true,
      hasBackButton: true,
      backgroundColor: AppAssets.origin().bgDetailTourColorV2,
      leading: AppAssets.init.iconArrowleft.widget(
        color: AppAssets.origin().whiteBackgroundColor,
      ),
      onLeadingPressed: () {
        ref.read(pTicketTourProvider.notifier).resetDetailedPage();
        Navigator.pop(context);
      },
      actions: [
        // GestureDetector(
        //   onTap: () {
        //     //TODO: share
        //   },
        //   child: AppAssets.init.icShare.widget(
        //     width: 24.H,
        //   ),
        // ),
        SizedBox(width: 16.W),
        TripCPortal(
            visible: isSave && visiblePortal,
            aligned: const Aligned(
                follower: Alignment.topRight,
                target: Alignment.bottomRight,
                offset: Offset(0, 5)),
            isBlur: false,
            icon: isSave
                ? AppAssets.init.icHeartFill.widget(width: 24.H)
                : AppAssets.init.icHeart.widget(width: 24.H),
            onClose: () {
              ref.read(pTicketTourProvider.notifier).onCloseSavedPopup();
            },
            onPressed: () async {
              if (globalCacheAuth.isLogged()) {
                final result =
                    await ref.read(pTicketTourProvider.notifier).savedTour();
                if (result) {
                  ref
                      .read(pHomepageScreenProvider.notifier)
                      .updatedDealsAroundHere(widget.tourId, !isSave);
                  ref
                      .read(pTicketTourProviderV2.notifier)
                      .updateListTour(widget.tourId, !isSave, widget.fromListType);
                }
              } else {
                dialogHelpers.show(context, child: NotYetLoginDialog(
                  resultHandler: () {
                    ref
                        .read(pTicketTourProvider.notifier)
                        .getDetailedTour(tourId: widget.tourId);
                  },
                ));
              }
            },
            follower: TourSavedView(
              onPressed: () {
                ref.read(pTicketTourProvider.notifier).onCloseSavedPopup();
                AppRoute.navigateToRoute(
                  context,
                  routeName: AppRoute.routeListToursView,
                  arguments: {
                    'category': TourType.tourSaved
                  },
                  resultHandler: (value) {
                    ref
                        .read(pTicketTourProvider.notifier)
                        .getDetailedTour(tourId: widget.tourId);
                  },
                );
              },
            ),
        ),
        SizedBox(
          width: 26.W,
        )
      ],
      body: Stack(
        children: [
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            bottom: 160.H,
            child: SingleChildScrollView(
              padding: EdgeInsets.zero,
              physics: const ClampingScrollPhysics(),
              child: Column(
                children: [
                  TripcImageSlide(
                    height: 223.H,
                    images:
                        selectedTour?.images.map((e) => e.url ?? '').toList() ??
                            [],
                  ),
                  TourTicketDetailViewV2(selectedTour: selectedTour),
                  // …more content…
                ],
              ),
            ),
          ),
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: Container(
              height: 160.H,
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border(
                  top: BorderSide(
                    color: Colors.grey.shade300,
                    width: 1.0,
                  ),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    offset: const Offset(0, -2),
                    blurRadius: 8.0,
                    spreadRadius: 1.0,
                  ),
                ],
              ),
              padding: EdgeInsets.symmetric(horizontal: 16.W, vertical: 8.H),
              child: _selectButton(
                isButtonEnable:
                    selectedTour?.serviceType == TripCServiceCategory.combo
                        ? true
                        : selectButtonEnable,
                context,
                selectedTour: selectedTour,
                onTapSelect: () => AppRoute.pushNamed(
                  context,
                  routeName: AppRoute.routeAddPassengerQuantityV2,
                  arguments: {
                    'tour': selectedTour,
                    'isFerryTour': widget.isFerryTour
                  },
                ),
                minTicketPrice: ref.watch(pTicketTourProvider).minTicketPrice,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _selectButton(BuildContext context,
      {required bool isButtonEnable,
      TourResponse? selectedTour,
      VoidCallback? onTapSelect,
      required int minTicketPrice}) {
    return Padding(
      padding: EdgeInsets.all(10.W),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TripcText(
                context.strings.ticket_price,
                fontSize: 16,
                fontWeight: FontWeight.w500,
                textAlign: TextAlign.start,
                textColor: AppAssets.origin().blackColor,
              ),
              TripcText(
                minTicketPrice.vnd,
                fontSize: 24,
                fontWeight: FontWeight.w700,
                textAlign: TextAlign.start,
                textColor: AppAssets.origin().priceServiceItemColor,
              ),
            ],
          ),
          SizedBox(
            height: 5.H,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TripcText(
                context.strings.surcharge_included,
                fontSize: 14,
                fontWeight: FontWeight.w400,
                textAlign: TextAlign.start,
                textColor: AppAssets.origin().disableColorV2,
              ),
              Visibility(
                visible: globalReleaseStatusNotifier.isDisplayAll,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppAssets.origin().bgTcentServiceItemColor,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      AppAssets.origin()
                          .icTcentV2
                          .widget(height: 20.W, width: 20.W),
                      TripcText(
                        '${context.strings.text_receive_now} ${(selectedTour?.returnTcent ?? 0).tcent}',
                        ignorePointer: true,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        textColor: AppAssets.origin().tcentServiceItemColor,
                        padding: EdgeInsets.only(left: 4.W),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          SizedBox(
            height: 8.H,
          ),
          TripcButton(
            onPressed: onTapSelect,
            showSuggestLoginDialog: true,
            textCase: TextCaseType.none,
            style: AppButtonStyle(
                backgroundColor: AppAssets.origin().primaryColorV2),
            isLogin: globalCacheAuth.isLogged(),
            resultHandler: () {
              ref
                  .read(pTicketTourProvider.notifier)
                  .getDetailedTour(tourId: widget.tourId);
            },
            // isButtonDisabled: !isButtonEnable,
            height: 46,
            title: context.strings.text_book_now.toSentenceCase(),
          )
        ],
      ),
    );
  }
}
