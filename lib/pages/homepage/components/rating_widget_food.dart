import 'package:flutter/cupertino.dart';
import 'package:tripc_app/models/remote/booking_response/supplier_responses/supplier_response.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_rich_text/tripc_rich_text.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class RatingWidgetFood extends StatelessWidget {
  final Supplier model;
  final Size iconSize;
  final double fontSize;
  final double addOnTextFontSize;
  final VoidCallback? onReviewTap;
  final EdgeInsets padding;

  const RatingWidgetFood(
      {super.key,
      required this.model,
      required this.iconSize,
      required this.fontSize,
      this.addOnTextFontSize = 10,
      this.onReviewTap,
      this.padding = EdgeInsets.zero});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onReviewTap ?? onReviewTap?.call();
      },
      child: Padding(
        padding: padding,
        child: Row(
          children: [
            AppAssets.origin()
                .icStarV2
                .widget(height: iconSize.height, width: iconSize.width),
            SizedBox(
              width: 4.W,
            ),
            ShaderMask(
              shaderCallback: (bounds) => const LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color(0xFFFFBE00),
                  Color(0xFFC32418),
                ],
                stops: [0.45, 1.0],
              ).createShader(
                Rect.fromLTWH(0, 0, bounds.width, bounds.height),
              ),
              child: TripcText(
                model.rating != 0 ? model.rating.toString() : '',
                fontSize: fontSize,
                fontWeight: FontWeight.w700,
                textColor: AppAssets.origin().whiteBackgroundColor,
              ),
            ),
            Flexible(
              child: TripcRichText(
                text: '',
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                children: [
                  TextSpan(
                    text: ' + ${model.totalReviews ?? 0} ',
                    style: AppAssets.origin().normalTextStyle.copyWith(
                        fontSize: addOnTextFontSize,
                        color: AppAssets.origin().secondDarkGreyTextColor),
                  ),
                  TextSpan(
                    text: context.strings.text_rating,
                    style: AppAssets.origin().normalTextStyle.copyWith(
                        fontSize: addOnTextFontSize,
                        color: AppAssets.origin().secondDarkGreyTextColor),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
