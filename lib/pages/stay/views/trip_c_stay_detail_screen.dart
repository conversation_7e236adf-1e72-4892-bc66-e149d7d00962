import 'package:flutter/material.dart';
import 'package:flutter_portal/flutter_portal.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:tripc_app/models/remote/booking_response/supplier_responses/supplier_response.dart';
import 'package:tripc_app/pages/homepage/components/rating_widget_food.dart';
import 'package:tripc_app/pages/stay/components/expanded_view_stay.dart';
import 'package:tripc_app/pages/stay/components/rating_widget_refund.dart';
import 'package:tripc_app/pages/stay/providers/trip_c_stay_refund_provider.dart';
import 'package:tripc_app/pages/ticket_tour/components/tour_saved_view.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/app_shimmer_loading/container_shimmer_loading.dart';
import 'package:tripc_app/widgets/commons/base_cached_network_image.dart';
import 'package:tripc_app/widgets/commons/tripc_portal/tripc_portal.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/extensions/context_extension.dart';
import '../components/search_gradient_background.dart';

class TripCStayDetailScreen extends ConsumerStatefulWidget {
  final int hotelId;

  const TripCStayDetailScreen({super.key, required this.hotelId});

  @override
  ConsumerState<TripCStayDetailScreen> createState() =>
      _TripCSearchDetailScreenState();
}

class _TripCSearchDetailScreenState
    extends ConsumerState<TripCStayDetailScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Stack(
      clipBehavior: Clip.none,
      children: [_imageHotel(), _mainHotelInformationCard(), _appBar()],
    ));
  }

  Widget _appBar() {
    // TODO: Handle favourite function here
    return SafeArea(
      child: Container(
        height: 25.H,
        padding: EdgeInsets.symmetric(horizontal: 16.W),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            GestureDetector(
              onTap: () => Navigator.pop(context),
              child: AppAssets.init.iconArrowleft.widget(
                color: AppAssets.origin().whiteBackgroundColor,
              ),
            ),
            TripcText(
              context.strings.refund_detail,
              fontWeight: FontWeight.w600,
              textColor: AppAssets.origin().whiteBackgroundColor,
            ),
            TripCPortal(
              visible: false,
              aligned: const Aligned(
                  follower: Alignment.topRight,
                  target: Alignment.bottomRight,
                  offset: Offset(0, 5)),
              isBlur: false,
              icon: false
                  ? AppAssets.init.icHeartFill.widget(width: 24.H)
                  : AppAssets.init.icHeart.widget(width: 24.H),
              onClose: () {
                // ref.read(pTicketTourProvider.notifier).onCloseSavedPopup();
              },
              onPressed: () async {
                // if (globalCacheAuth.isLogged()) {
                //   final result =
                //   await ref.read(pTicketTourProvider.notifier).savedTour();
                //   if (result) {
                //     ref
                //         .read(pHomepageScreenProvider.notifier)
                //         .updatedDealsAroundHere(widget.tourId, !isSave);
                //     ref
                //         .read(pTicketTourProviderV2.notifier)
                //         .updateListTour(widget.tourId, !isSave, widget.fromListType);
                //   }
                // } else {
                //   dialogHelpers.show(context, child: NotYetLoginDialog(
                //     resultHandler: () {
                //       ref
                //           .read(pTicketTourProvider.notifier)
                //           .getDetailedTour(tourId: widget.tourId);
                //     },
                //   ));
                // }
              },
              follower: TourSavedView(
                onPressed: () {
                  // ref.read(pTicketTourProvider.notifier).onCloseSavedPopup();
                  // AppRoute.navigateToRoute(
                  //   context,
                  //   routeName: AppRoute.routeListToursView,
                  //   arguments: {
                  //     'category': TourType.tourSaved
                  //   },
                  //   resultHandler: (value) {
                  //     ref
                  //         .read(pTicketTourProvider.notifier)
                  //         .getDetailedTour(tourId: widget.tourId);
                  //   },
                  // );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _imageHotel() {
    final backgroundColor = AppAssets.origin().barrier80;
    final color = AppAssets.origin().grayEFColor;
    return Stack(
      children: [
        BaseCachedNetworkImage(
          height: 270.H,
          imageUrl:
              'https://drive.google.com/uc?export=view&id=1mSIlvh8iOZZgb2dTyj5XaSR9f5p6TmUf',
          placeholder: (context, _) => ContainerLoading(
            borderRadius: BorderRadius.zero,
            enableShimmer: true,
            height: 270.H,
            highlightColor: color,
            color: backgroundColor,
          ),
          errorWidget: (context, error, stackTrace) =>
              AppAssets.origin().icErrorImg.widget(
                    color: context.appCustomPallet.buttonBG,
                  ),
          fit: BoxFit.fill,
        ),
        Container(
          height: 270.H,
          decoration: BoxDecoration(
            gradient: AppAssets.origin().refundDetailPageGradient,
          ),
        )
      ],
    );
  }

  Widget _mainHotelInformationCard() {
    return Positioned(
      left: 0,
      right: 0,
      top: 246,
      child: Container(
        decoration: BoxDecoration(
          color: AppAssets.origin().whiteBackgroundColor,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(24.SP),
            topRight: Radius.circular(24.SP),
          ),
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.W),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 24.H,
            children: [
              _informationCard(),
              _dateRangePicker(),
              _descriptionCard()
            ],
          ),
        ),
      ),
    );
  }

  Widget _informationCard() {
    // Mock up data hotel
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: 2.H,
      children: [
        SizedBox(
          height: 24.H,
        ),
        TripcText(
          'The Da Nang Oceanview',
          fontSize: 20.SP,
          fontWeight: FontWeight.w500,
          height: 28 / 20,
          textColor: AppAssets.origin().colorTextFoodName,
        ),
        RatingWidgetRefund(
          rating: 4.4,
          addOnValue: 200,
          iconSize: Size(
            14.W,
            14.W,
          ),
        ),
        SizedBox(
          height: 6.H,
        ),
        Row(
          spacing: 4.W,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(
              Icons.location_on_sharp,
              color: AppAssets.origin().textForLocationTourV2,
              size: 16.W,
            ),
            Expanded(
              child: TripcText(
                '22 Trần Hưng Đạo, Phường Mỹ An, Quận Ngũ Hành Sơn, Thành phố Đà Nẵng',
                textAlign: TextAlign.start,
                textColor: AppAssets.origin().hotelSearchNameColor,
                fontSize: 12.SP,
                fontWeight: FontWeight.w400,
                height: 16 / 12,
              ),
            )
          ],
        )
      ],
    );
  }

  Widget _dateRangePicker() {
    return GestureDetector(
      onTap: () {
        print("object");
      },
      child: Container(
        padding: EdgeInsets.all(16.W),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.SP),
          border: Border.all(color: AppAssets.origin().disableDot),
        ),
        child: Row(
          children: [
            AppAssets.origin().icCalendar.widget(
                height: 20.H,
                width: 20.H,
                color: AppAssets.origin().colorTextFoodName,
            ),
            SizedBox(
              width: 8.W,
            ),
            TripcText(
              'Th 7, 29 thg 4 - Cn, 30 thg 4',
              textColor: AppAssets.origin().colorTextFoodName,
              fontSize: 14.SP,
              fontWeight: FontWeight.w400,
              height: 20 / 14,
            ),
            Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Container(
                  width: 1.W,
                  height: 20.H,
                  color: AppAssets.origin().disableDot,
                ),
                SizedBox(width: 15.W,),
                TripcText(
                  context.strings.night_with_count(1),
                  textColor: AppAssets.origin().colorTextFoodName,
                  fontSize: 14.SP,
                  fontWeight: FontWeight.w400,
                  height: 20 / 14,
                ),
                SizedBox(width: 8.W,),
                AppAssets.origin().iconRight.widget(
                  height: 20.H,
                  width: 20.H,
                  color: AppAssets.origin().darkBlueColor,
                ),
              ],
            )),
          ],
        ),
      ),
    );
  }

  Widget _descriptionCard() {
    return ExpandedViewRefund(
      title: context.strings.description,
      content: TripcText("Vị trí bên bờ biển: Cam Ranh Oceanview ở Cam Ranh có lối đi thẳng ra bờ biển với khu vực bãi biển riêng. Du khách có thể tận hưởng tầm nhìn tuyệt đẹp ra biển và hồ bơi ngoài trời quanh năm. ",
        textColor: AppAssets.origin().hotelSearchNameColor,
        fontSize: 14.SP,
        height: 20 / 14,
        fontWeight: FontWeight.w400,
      ),
    );
  }
}
