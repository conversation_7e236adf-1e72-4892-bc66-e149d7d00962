import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/pages/stay/components/stay_refund_item.dart';
import 'package:tripc_app/pages/stay/providers/trip_c_stay_refund_provider.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/extensions/context_extension.dart';
import '../components/search_gradient_background.dart';
import '../components/upload_evidence_bottom_sheet.dart';

class TripCStayRefundDetailScreen extends ConsumerStatefulWidget {
  final int refundId;

  const TripCStayRefundDetailScreen({super.key, required this.refundId});

  @override
  ConsumerState<TripCStayRefundDetailScreen> createState() =>
      _TripCSearchDetailScreenState();
}

class _TripCSearchDetailScreenState
    extends ConsumerState<TripCStayRefundDetailScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SearchGradientBackground(
        child: _mainView(),
      ),
    );
  }

  Widget _mainView() {
    // TODO: Fill data from refund detail result
    final state = ref.watch(pStayRefundProvider);
    final notifier = ref.read(pStayRefundProvider.notifier);
    return SafeArea(
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 15.W),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 16.H,
            children: [
              _appBar(),
              SizedBox(
                height: 10.H,
              ),
              _refundDetailStatusCard(),
              TripcText(
                context.strings.refund_history,
                textColor: AppAssets.origin().colorTextFoodName,
                fontSize: 18.SP,
                height: 1.33,
                fontWeight: FontWeight.w500,
              ),
              // ListView.separated(
              //     shrinkWrap: true,
              //     physics: const NeverScrollableScrollPhysics(),
              //     itemBuilder: (context, index) {
              //       final refundStay = state.listRefunds[index];
              //       return StayRefundItem(refundStay);
              //     },
              //     separatorBuilder: (_, __) => SizedBox(
              //           height: 16.H,
              //         ),
              //     itemCount: state.listRefunds.length,
              // ),
              SizedBox(
                height: 20.H,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _appBar() {
    return Stack(
      alignment: Alignment.center,
      children: [
        Align(
          alignment: Alignment.centerLeft,
          child: GestureDetector(
            onTap: () => Navigator.pop(context),
            child: AppAssets.init.iconArrowleft.widget(
              color: AppAssets.origin().whiteBackgroundColor,
            ),
          ),
        ),
        TripcText(
          context.strings.refund_detail,
          fontWeight: FontWeight.w600,
          textColor: AppAssets.origin().whiteBackgroundColor,
        ),
      ],
    );
  }

  Widget _refundDetailStatusCard() {
    return _mainCard(
      children: [
        _statusCardInformation(),

      ],
    );
  }

  Widget _mainCard({required List<Widget> children}) {
    return Container(
      padding: EdgeInsets.all(16.W),
      decoration: BoxDecoration(
        color: AppAssets.origin().whiteBackgroundColor,
        borderRadius: BorderRadius.circular(16.SP),
      ),
      child: Column(
        spacing: 16.H,
        children: children,
      ),
    );
  }

  Widget _statusCardInformation() {
    // TODO: Fill detail status text
    return Row(
      children: [
        Expanded(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TripcText(
                "Đơn đã đặt",
                fontWeight: FontWeight.w500,
                fontSize: 18.SP,
                height: 24 / 18,
                textColor: AppAssets.origin().blue191,
              ),
              TripcText(
                "Đang chờ xác nhận từ nhà cung cấp",
                fontSize: 14.SP,
                height: 4 / 3,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                textColor: AppAssets.origin().blue001,
              ),
            ],
          ),
        ),
        Container(
          margin: EdgeInsets.only(left: 26.W),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8.SP),
              border: Border.all(color: AppAssets.origin().darkBlueColor)),
          // Change to BaseCachedNetworkImage(
          //                 imageUrl: item.image,
          //                 height: 40.W,
          //                 width: 40.W,
          //                 placeholder: (context, _) => Container(
          //                   color: AppAssets.origin().lightGrayDD4,
          //                 ),
          //                 errorWidget: (context, error, stackTrace) =>
          //                     AppAssets.origin().icErrorImg.widget(
          //                           color: context.appCustomPallet.buttonBG,
          //                         ),
          //                 fit: BoxFit.fitHeight,
          //               )),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(7.SP),
            child: Image.network(
              "https://images.seeklogo.com/logo-png/37/1/agoda-logo-png_seeklogo-371025.png",
              height: 40.W,
              width: 40.W,
              fit: BoxFit.cover,
              headers: const {
                'User-Agent':
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Referer': 'https://www.google.com/',
              },
              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return Container(
                  color: AppAssets.origin().lightGrayDD4,
                  child: Center(
                    child: SizedBox(
                      width: 20.W,
                      height: 20.W,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: AppAssets.origin().primaryColorV2,
                        value: loadingProgress.expectedTotalBytes != null
                            ? loadingProgress.cumulativeBytesLoaded /
                                loadingProgress.expectedTotalBytes!
                            : null,
                      ),
                    ),
                  ),
                );
              },
              errorBuilder: (context, error, stackTrace) {
                debugPrint('Image loading error: $error');
                return Container(
                  color: AppAssets.origin().lightGrayDD4,
                  child: AppAssets.origin().icErrorImg.widget(
                        color: context.appCustomPallet.buttonBG,
                      ),
                );
              },
            ),
          ),
        ),
      ],
    );
  }
}
