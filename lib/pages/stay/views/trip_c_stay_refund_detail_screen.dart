import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:tripc_app/pages/stay/providers/trip_c_stay_refund_provider.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/app_shimmer_loading/container_shimmer_loading.dart';
import 'package:tripc_app/widgets/commons/base_cached_network_image.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/extensions/context_extension.dart';
import '../components/search_gradient_background.dart';

class TripCStayRefundDetailScreen extends ConsumerStatefulWidget {
  final int refundId;

  const TripCStayRefundDetailScreen({super.key, required this.refundId});

  @override
  ConsumerState<TripCStayRefundDetailScreen> createState() =>
      _TripCSearchDetailScreenState();
}

class _TripCSearchDetailScreenState
    extends ConsumerState<TripCStayRefundDetailScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SearchGradientBackground(
        child: _mainView(),
      ),
    );
  }

  Widget _mainView() {
    // TODO: Fill data from refund detail result
    final state = ref.watch(pStayRefundProvider);
    final notifier = ref.read(pStayRefundProvider.notifier);
    final historyWithdrawal = [];
    return SafeArea(
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 15.W),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 13.H,
            children: [
              _appBar(),
              SizedBox(
                height: 13.H,
              ),
              _refundDetailStatusCard(),
              _refundInformationCard(),
              _listEvidenceImageCard(),
              _withDrawableAmountCard(),
              TripcText(
                context.strings.withdrawal_history,
                textColor: AppAssets.origin().colorTextFoodName,
                fontSize: 18.SP,
                height: 1.33,
                fontWeight: FontWeight.w500,
              ),
              historyWithdrawal.isNotEmpty ? ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemBuilder: (context, index) {
                  return const TripcText("name");
                },
                separatorBuilder: (_, __) => SizedBox(
                  height: 16.H,
                ),
                itemCount: state.listRefunds.length,
              ) : _emptyHistory(),
              SizedBox(
                height: 20.H,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _appBar() {
    return Stack(
      alignment: Alignment.center,
      children: [
        Align(
          alignment: Alignment.centerLeft,
          child: GestureDetector(
            onTap: () => Navigator.pop(context),
            child: AppAssets.init.iconArrowleft.widget(
              color: AppAssets.origin().whiteBackgroundColor,
            ),
          ),
        ),
        TripcText(
          context.strings.refund_detail,
          fontWeight: FontWeight.w600,
          textColor: AppAssets.origin().whiteBackgroundColor,
        ),
      ],
    );
  }

  Widget _withDrawableAmountCard() {
    return _mainCardCommon(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
                child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: 2.H,
              children: [
                TripcText(
                  context.strings.amount_available_for_withdrawal,
                  fontSize: 12.SP,
                  height: 16 / 12,
                  fontWeight: FontWeight.w400,
                  textColor: AppAssets.origin().colorTextFoodName,
                ),
                TripcText(
                  673544.vnd,
                  fontSize: 16.SP,
                  height: 22 / 16,
                  fontWeight: FontWeight.w700,
                  textColor: AppAssets.origin().colorTextFoodName,
                ),
                SizedBox(
                  height: 2.H,
                ),
                TripcText(
                  '${context.strings.minimum_withdrawal_amount}: ${400000.vnd}',
                  fontSize: 12.SP,
                  height: 16 / 12,
                  fontWeight: FontWeight.w400,
                  textColor: AppAssets.origin().colorTextFoodName,
                ),
                SizedBox(
                  height: 3.H,
                ),
                TripcText(
                  '${context.strings.maximum_withdrawal_amount_per_day}: ${5000000.vnd}',
                  fontSize: 12.SP,
                  height: 16 / 12,
                  fontWeight: FontWeight.w400,
                  textAlign: TextAlign.start,
                  textColor: AppAssets.origin().colorTextFoodName,
                ),
              ],
            )),
            GestureDetector(
              onTap: () {
                // TODO: Handle event for draw button click
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 12.W, vertical: 6.H),
                decoration: BoxDecoration(
                  color: AppAssets.origin().textForLocationTourV2,
                  borderRadius: BorderRadius.circular(8.SP),
                ),
                child: TripcText(
                  context.strings.withdraw_money,
                  textColor: AppAssets.origin().whiteBackgroundColor,
                  fontSize: 14.SP,
                  fontWeight: FontWeight.w500,
                  height: 1.42,
                ),
              ),
            ),
          ],
        )
      ],
    );
  }

  Widget _refundDetailStatusCard() {
    return _mainCardCommon(
      children: [
        _statusCardInformation(),
        _refundStatusStepper(),
        _estimateRefundTime()
      ],
    );
  }

  Widget _refundInformationCard() {
    return _mainCardCommon(children: [
      TripcText(
        context.strings.tracking_information,
        fontWeight: FontWeight.w500,
        fontSize: 18.SP,
        height: 24 / 18,
        textColor: AppAssets.origin().blue191,
      ),
      Column(
        spacing: 12.H,
        children: [
          _informationCard(context.strings.branch, 'Agoda'),
          _informationCard(context.strings.service, 'Lưu trú'),
          _informationCard(context.strings.order_time, '29/04/2025, 12:22 PM'),
          _informationCard(context.strings.order_code, '1234567890'),
          _informationCard(context.strings.total_amount, 1500000.vnd),
          _informationCard(context.strings.refunded_amount, 120000.vnd),
        ],
      )
    ]);
  }

  Widget _listEvidenceImageCard() {
    // TODO: Refactor for ContainerLoading
    final backgroundColor = AppAssets.origin().barrier80;
    final color = AppAssets.origin().grayEFColor;
    List<String> images = [
      'https://picsum.photos/200/300',
      'https://picsum.photos/200/300',
      'https://picsum.photos/200/300'
    ];
    return _mainCardCommon(children: [
      TripcText(
        context.strings.image_uploaded,
        fontWeight: FontWeight.w500,
        fontSize: 18.SP,
        height: 24 / 18,
        textColor: AppAssets.origin().blue191,
      ),
      ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          final image = images[index];
          return BaseCachedNetworkImage(
            imageUrl: image,
            placeholder: (context, _) => ContainerLoading(
              borderRadius: BorderRadius.zero,
              enableShimmer: true,
              height: 500.H,
              width: 311.W,
              highlightColor: color,
              color: backgroundColor,
            ),
            errorWidget: (context, error, stackTrace) =>
                AppAssets.origin().icErrorImg.widget(
                      color: context.appCustomPallet.buttonBG,
                    ),
            fit: BoxFit.fitHeight,
          );
        },
        separatorBuilder: (_, __) => SizedBox(
          height: 16.H,
        ),
        itemCount: images.length,
      ),
    ]);
  }

  Widget _mainCardCommon({required List<Widget> children}) {
    return Container(
      padding: EdgeInsets.all(16.W),
      decoration: BoxDecoration(
        color: AppAssets.origin().whiteBackgroundColor,
        borderRadius: BorderRadius.circular(16.SP),
      ),
      child: Column(
        spacing: 16.H,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: children,
      ),
    );
  }

  Widget _statusCardInformation() {
    // TODO: Fill detail status text
    return Row(
      children: [
        Expanded(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TripcText(
                context.strings.order_placed,
                fontWeight: FontWeight.w500,
                fontSize: 18.SP,
                height: 24 / 18,
                textColor: AppAssets.origin().blue191,
              ),
              TripcText(
                "Đang chờ xác nhận từ nhà cung cấp",
                fontSize: 14.SP,
                height: 4 / 3,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                textColor: AppAssets.origin().blue001,
              ),
            ],
          ),
        ),
        Container(
          margin: EdgeInsets.only(left: 26.W),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8.SP),
              border: Border.all(color: AppAssets.origin().darkBlueColor)),
          // Change to BaseCachedNetworkImage(
          //                 imageUrl: item.image,
          //                 height: 40.W,
          //                 width: 40.W,
          //                 placeholder: (context, _) => Container(
          //                   color: AppAssets.origin().lightGrayDD4,
          //                 ),
          //                 errorWidget: (context, error, stackTrace) =>
          //                     AppAssets.origin().icErrorImg.widget(
          //                           color: context.appCustomPallet.buttonBG,
          //                         ),
          //                 fit: BoxFit.fitHeight,
          //               )),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(7.SP),
            child: Image.network(
              "https://images.seeklogo.com/logo-png/37/1/agoda-logo-png_seeklogo-371025.png",
              height: 48.W,
              width: 48.W,
              fit: BoxFit.cover,
              headers: const {
                'User-Agent':
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Referer': 'https://www.google.com/',
              },
              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return Container(
                  color: AppAssets.origin().lightGrayDD4,
                  child: Center(
                    child: SizedBox(
                      width: 20.W,
                      height: 20.W,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: AppAssets.origin().primaryColorV2,
                        value: loadingProgress.expectedTotalBytes != null
                            ? loadingProgress.cumulativeBytesLoaded /
                                loadingProgress.expectedTotalBytes!
                            : null,
                      ),
                    ),
                  ),
                );
              },
              errorBuilder: (context, error, stackTrace) {
                debugPrint('Image loading error: $error');
                return Container(
                  color: AppAssets.origin().lightGrayDD4,
                  child: AppAssets.origin().icErrorImg.widget(
                        color: context.appCustomPallet.buttonBG,
                      ),
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _refundStatusStepper() {
    // TODO: Get current step from refund status data
    final currentStep = 2; // 0: Requested, 1: Processing, 2: Refunded, 3: Done

    final steps = [
      _RefundStep(
        icon: AppAssets.origin().iconStatusRefundRequested,
        title: "Đã yêu cầu",
        isActive: currentStep >= 0,
        isCompleted: currentStep > 0,
      ),
      _RefundStep(
        icon: AppAssets.origin().iconStatusRefundProcessing,
        title: "Đang xử lý",
        isActive: currentStep >= 1,
        isCompleted: currentStep > 1,
      ),
      _RefundStep(
        icon: AppAssets.origin().iconStatusRefunded,
        title: "Hoàn tất",
        isActive: currentStep >= 2,
        isCompleted: currentStep > 2,
      ),
      _RefundStep(
        icon: AppAssets.origin().iconStatusRefundDone,
        title: "Hoàn tiền",
        isActive: currentStep >= 3,
        isCompleted: currentStep > 3,
      ),
    ];

    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        for (int i = 0; i < steps.length; i++) ...[
          _buildStepItem(
              steps[i], i == 0, i == steps.length - 1, steps[i].isCompleted),
        ],
      ],
    );
  }

  Widget _buildStepItem(
      _RefundStep step, bool isFirst, bool isLast, bool isComplete) {
    return Expanded(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildFirstConnector(
                  isFirst, step.isCompleted, !(isFirst || isLast)),
              step.icon.widget(
                color: step.isActive
                    ? AppAssets.origin().blue0365
                    : AppAssets.origin().grayC4C8,
              ),
              _buildSecondConnector(isLast, step.isCompleted),
            ],
          ),
          SizedBox(
            height: 10.H,
          ),
          TripcText(
            step.title,
            fontSize: 13.SP,
            fontWeight: FontWeight.w500,
            textColor: step.isActive
                ? AppAssets.origin().blue191
                : AppAssets.origin().colorStatusDisableStepRefund,
          ),
        ],
      ),
    );
  }

  Widget _buildFirstConnector(bool isFirst, bool isCompleted, bool isBetween) {
    return Expanded(
      child: Container(
        height: 2.H,
        decoration: BoxDecoration(
          color: isFirst
              ? AppAssets.origin().whiteBackgroundColor
              : (isCompleted || isBetween)
                  ? AppAssets.origin().darkBlueColor
                  : AppAssets.origin().grayC4C8,
        ),
      ),
    );
  }

  Widget _buildSecondConnector(bool isLast, bool isCompleted) {
    return Expanded(
      child: Container(
        height: 2.H,
        decoration: BoxDecoration(
          color: isLast
              ? AppAssets.origin().whiteBackgroundColor
              : isCompleted
                  ? AppAssets.origin().darkBlueColor
                  : AppAssets.origin().grayC4C8,
        ),
      ),
    );
  }

  Widget _estimateRefundTime() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 30.W, vertical: 8.H),
      decoration: BoxDecoration(
          color: AppAssets.origin().colorEstimateCardDateRefund,
          borderRadius: BorderRadius.circular(8.SP)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          TripcText(
            '${context.strings.estimated_refund_time}: ',
            fontSize: 14.SP,
            height: 20 / 14,
            textColor: AppAssets.origin().textForLocationTourV2,
          ),
          TripcText(
            '02/07/2025',
            fontSize: 14.SP,
            height: 20 / 14,
            fontWeight: FontWeight.w500,
            textColor: AppAssets.origin().textForLocationTourV2,
          ),
        ],
      ),
    );
  }

  Widget _informationCard(String title, String content) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 12,
          child: TripcText(
            '${title}: ',
            fontSize: 14.SP,
            height: 20 / 14,
            textAlign: TextAlign.start,
            textColor: AppAssets.origin().blue001,
          ),
        ),
        Expanded(
          flex: 19,
          child: TripcText(
            content,
            fontWeight: FontWeight.w600,
            fontSize: 14.SP,
            height: 20 / 14,
            textAlign: TextAlign.end,
            textColor: AppAssets.origin().hotelSearchNameColor,
          ),
        )
      ],
    );
  }

  Widget _emptyHistory() {
    return SizedBox(
      height: 350.H,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          spacing: 11.H,
          children: [
            AppAssets.origin().emptyHistoryWithdrawal.widget(),
            TripcText(
              'Chưa có lịch sử rút tiền.\nSố tiền được rút tối thiểu: 400,000 VND',
              fontWeight: FontWeight.w400,
              fontSize: 12.SP,
              height: 16 / 12,
              textColor: AppAssets.origin().gray474,
            ),
          ],
        ),
      ),
    );
  }
}

class _RefundStep {
  final AppAssetBuilder icon;
  final String title;
  final bool isActive;
  final bool isCompleted;

  _RefundStep({
    required this.icon,
    required this.title,
    required this.isActive,
    required this.isCompleted,
  });
}
