import 'package:flutter/material.dart';
import 'package:tripc_app/models/app/stay_type_enum/refund_type_enum.dart';
import 'package:tripc_app/models/remote/stay_response.dart/refund_model.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

import 'package:tripc_app/widgets/extensions/context_extension.dart';

class StayRefundItem extends StatelessWidget {
  final RefundModel item;
  final VoidCallback onPressed;

  const StayRefundItem({super.key, required this.item, required this.onPressed});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        padding: EdgeInsets.all(8.W),
        decoration: BoxDecoration(
          color: AppAssets.origin().whiteBackgroundColor,
          borderRadius: BorderRadius.circular(8.SP),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 8.W,
          children: [
            Container(
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8.SP),
                  border: Border.all(color: AppAssets.origin().darkBlueColor)),
              // Change to BaseCachedNetworkImage(
              //                 imageUrl: item.image,
              //                 height: 40.W,
              //                 width: 40.W,
              //                 placeholder: (context, _) => Container(
              //                   color: AppAssets.origin().lightGrayDD4,
              //                 ),
              //                 errorWidget: (context, error, stackTrace) =>
              //                     AppAssets.origin().icErrorImg.widget(
              //                           color: context.appCustomPallet.buttonBG,
              //                         ),
              //                 fit: BoxFit.fitHeight,
              //               )),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(7.SP),
                child: Image.network(
                  item.image,
                  height: 40.W,
                  width: 40.W,
                  fit: BoxFit.cover,
                  headers: const {
                    'User-Agent':
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Referer': 'https://www.google.com/',
                  },
                  loadingBuilder: (context, child, loadingProgress) {
                    if (loadingProgress == null) return child;
                    return Container(
                      color: AppAssets.origin().lightGrayDD4,
                      child: Center(
                        child: SizedBox(
                          width: 20.W,
                          height: 20.W,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: AppAssets.origin().primaryColorV2,
                            value: loadingProgress.expectedTotalBytes != null
                                ? loadingProgress.cumulativeBytesLoaded /
                                    loadingProgress.expectedTotalBytes!
                                : null,
                          ),
                        ),
                      ),
                    );
                  },
                  errorBuilder: (context, error, stackTrace) {
                    debugPrint('Image loading error: $error');
                    return Container(
                      color: AppAssets.origin().lightGrayDD4,
                      child: AppAssets.origin().icErrorImg.widget(
                            color: context.appCustomPallet.buttonBG,
                          ),
                    );
                  },
                ),
              ),
            ),
            Flexible(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                spacing: 2.H,
                children: [
                  TripcText(
                    item.name,
                    textColor: AppAssets.origin().blue191,
                    fontSize: 14.SP,
                    height: 1.42,
                    fontWeight: FontWeight.w600,
                  ),
                  TripcText(
                    context.strings.text_stay.capitalizeFirstLetter,
                    textColor: AppAssets.origin().colorTextFoodName,
                    fontSize: 10.SP,
                    height: 1.4,
                  ),
                  _colTextInformation(
                      '${context.strings.room_amount_including_taxes_fees}:',
                      item.amount.vnd),
                  _colTextInformation('${context.strings.estimated_refund_time}:',
                      item.estimateTime.formatddMMYYY())
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              spacing: 4.H,
              children: [
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.W, vertical: 2.H),
                  decoration: BoxDecoration(
                      color: RefundEnum.getByValue(item.statusCode)
                          .backgroundColor(),
                      borderRadius: BorderRadius.circular(8.SP)),
                  child: TripcText(
                    RefundEnum.getByValue(item.statusCode).title(context),
                    textColor: RefundEnum.getByValue(item.statusCode).textColor(),
                    fontSize: 8.SP,
                    height: 1.5,
                  ),
                ),
                TripcText(
                  '${context.strings.text_refund} ${item.returnAmount.vnd}',
                  textColor: AppAssets.origin().colorTextFoodName,
                  fontSize: 10.SP,
                  height: 1.4,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  fontWeight: FontWeight.w500,
                ),
              ],
            )
          ],
        ),
      ),
    );
  }

  Widget _colTextInformation(String title, String content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TripcText(
          title,
          textColor: AppAssets.origin().colorInformationTextRefundItem,
          fontSize: 10.SP,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
          height: 1.4,
        ),
        TripcText(
          content,
          textColor: AppAssets.origin().hotelSearchNameColor,
          fontSize: 14.SP,
          height: 1.42,
          fontWeight: FontWeight.w500,
        ),
      ],
    );
  }
}
