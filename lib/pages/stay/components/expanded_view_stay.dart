import 'package:flutter/material.dart';
import 'package:tripc_app/utils/app_extension.dart';

import '../../../services/app/app_assets.dart';
import '../../../widgets/commons/tripc_text/tripc_text.dart';

class ExpandedViewRefund extends StatefulWidget {
  final String title;
  final Widget content;

  const ExpandedViewRefund({super.key,
    required this.title,
    required this.content,
  });

  @override
  ExpandedViewRefundState createState() => ExpandedViewRefundState();
}

class ExpandedViewRefundState extends State<ExpandedViewRefund> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () {
              print("jsaijdasd");
              setState(() {
                _isExpanded = !_isExpanded;
              });
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                TripcText(widget.title,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    textAlign: TextAlign.start,
                    height: 22 / 16,
                    textColor: AppAssets.origin().blue191),
                AnimatedRotation(
                  turns: _isExpanded ? -0.5 : 0,
                  duration: const Duration(milliseconds: 300),
                  child: Icon(Icons.keyboard_arrow_down, color: AppAssets.origin().colorTextFoodName,),
                ),
              ],
            ),
          ),
        ),
        AnimatedCrossFade(
          firstChild: Container(),
          secondChild: Container(
            padding: EdgeInsets.only(top: 12.H),
            width: double.infinity,
            child: widget.content,
          ),
          crossFadeState: _isExpanded
              ? CrossFadeState.showSecond
              : CrossFadeState.showFirst,
          duration: const Duration(milliseconds: 300),
        ),
      ],
    );
  }
}