import 'package:flutter/cupertino.dart';
import 'package:tripc_app/models/remote/booking_response/supplier_responses/supplier_response.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_rich_text/tripc_rich_text.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class RatingWidgetRefund extends StatelessWidget {
  final double rating;
  final int addOnValue;
  final Size iconSize;
  final VoidCallback? onReviewTap;

  const RatingWidgetRefund(
      {super.key,
        required this.rating,
        required this.addOnValue,
        required this.iconSize,
        this.onReviewTap,});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onReviewTap ?? onReviewTap?.call();
      },
      child: Row(
        children: [
          AppAssets.origin()
              .icStarV2
              .widget(height: iconSize.height, width: iconSize.width),
          SizedBox(
            width: 4.W,
          ),
          ShaderMask(
            shaderCallback: (bounds) => const LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Color(0xFFFFBE00),
                Color(0xFFC32418),
              ],
              stops: [0.45, 1.0],
            ).createShader(
              Rect.fromLTWH(0, 0, bounds.width, bounds.height),
            ),
            child: TripcText(
              rating.toString(),
              fontSize: 12,
              fontWeight: FontWeight.w700,
              height: 16 / 12,
              textColor: AppAssets.origin().whiteBackgroundColor,
            ),
          ),
          TripcText(
            ' (${addOnValue.toString()} ${context.strings.text_rating})',
            fontWeight: FontWeight.w400,
            height: 16 / 12,
            fontSize: 12,
            textColor: AppAssets.origin().secondDarkGreyTextColor
          )
        ],
      ),
    );
  }
}
