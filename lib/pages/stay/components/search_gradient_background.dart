import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';

class SearchGradientBackground extends StatelessWidget {
  final Widget child;
  final double? height;
  final double? gradientHeight;
  final double? blurCircleTop;
  final double? blurCircleLeft;
  final double? blurCircleSize;

  const SearchGradientBackground({
    super.key,
    required this.child,
    this.height,
    this.gradientHeight,
    this.blurCircleTop,
    this.blurCircleLeft,
    this.blurCircleSize,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Background color
        Container(
          color: AppAssets.origin().backgroundSearchStay,
        ),
        // Gradient with blur effect
        SizedBox(
          height: height ?? 228.H,
          child: Stack(
            children: [
              Align(
                alignment: Alignment.topCenter,
                child: Container(
                  height: gradientHeight ?? 228.H,
                  decoration:
                      BoxDecoration(gradient: AppAssets.origin().searchGradient),
                ),
              ),
              Positioned(
                top: -6.H,
                left: -17.W,
                child: Container(
                  height: blurCircleSize ?? 158.W,
                  width: blurCircleSize ?? 158.W,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        blurRadius: 150,
                        spreadRadius: 10,
                        color: const Color(0xFF205EE4).withValues(alpha: 0.25),
                      )
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        // Child content
        child,
      ],
    );
  }
}
